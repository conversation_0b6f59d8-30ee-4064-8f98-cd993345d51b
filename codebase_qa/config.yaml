# Codebase QA 配置文件

# 模型配置
models:
  local:
    api_base: "http://localhost:11434"
    embedding_model: "text-embedding-qwen3-embedding-0.6b"
    llm_model: "qwen3-30b-a3b-mlx@4bit"
    timeout: 30
    max_retries: 3
  
  fallback:
    embedding_model: "all-MiniLM-L6-v2"
    enable_fallback: true

# 检索配置
retrieval:
  chunk_size: 50
  overlap_lines: 10
  min_chunk_chars: 100
  bm25_top_k: 10
  embedding_top_k: 10
  final_top_k: 5
  enable_rerank: true

# 索引配置
indexing:
  persist_path: "./data/indexes"
  supported_extensions:
    - ".py"
    - ".js"
    - ".java"
    - ".cpp"
    - ".c"
    - ".h"
    - ".ts"
    - ".go"
    - ".rs"
  ignore_dirs:
    - ".git"
    - "__pycache__"
    - "node_modules"
    - ".vscode"
    - ".idea"
    - "dist"
    - "build"

# 评测配置
evaluation:
  k_values: [1, 3, 5, 10]
  timeout_per_query: 30
  save_detailed_results: true
  output_format: "json"
  
  dataset:
    chunk_size: 50
    overlap_lines: 10
    min_chunk_chars: 100
    max_queries_per_file: 5
    difficulty_distribution:
      easy: 0.3
      medium: 0.5
      hard: 0.2

# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "./logs/codebase_qa.log"
  max_size: "10MB"
  backup_count: 5

# MCP配置
mcp:
  server_name: "codebase-qa"
  version: "1.0.0"
  tools:
    - "search_code"
    - "read_file"
    - "analyze_structure"
    - "get_file_info"
