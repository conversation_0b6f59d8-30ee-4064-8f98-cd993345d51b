# Codebase QA - 代码库问答系统

一个完整的代码库问答系统，支持自然语言查询、多种检索策略、MCP协议接口和全面的评测框架。

## 🚀 功能特性

### 核心功能
- 🔍 **智能代码检索**: 支持BM25、Embedding、Graph多种检索策略
- 🧠 **多Pipeline架构**: Simple、Pro、Balance、Best四种不同的检索管道
- 🔄 **结果重排序**: 使用本地LLM提升检索质量
- 📊 **统一结果格式**: 标准化的响应格式和结果处理

### 本地化部署
- 🏠 **完全本地运行**: 支持本地LLM和Embedding模型
- 🔧 **模型适配器**: 适配LM Studio等本地模型服务
- 📱 **MCP协议支持**: 可直接集成到Claude Desktop等客户端
- 🛠️ **丰富的工具**: 代码搜索、文件读取、结构分析等

### 评测系统
- 📈 **多维度指标**: Precision、Recall、NDCG、MAP、MRR等
- 🤖 **智能数据生成**: 基于代码分析自动生成高质量评测数据
- 📊 **自动化评测**: 一键运行完整评测流程
- 📋 **详细报告**: 生成全面的性能分析报告

## 📁 项目结构

```
codebase_qa/
├── core/                   # 核心功能模块
│   ├── retrieval/         # 检索器实现
│   ├── pipeline/          # 检索管道
│   └── utils/             # 工具函数
├── evaluation/            # 评测系统
│   ├── datasets/          # 数据集管理
│   ├── metrics/           # 指标计算
│   └── runners/           # 评测执行器
├── models/                # 模型相关
│   ├── local/             # 本地模型
│   └── adapters/          # 模型适配器
├── data/                  # 数据存储
│   ├── indexes/           # 索引文件
│   └── cache/             # 缓存文件
├── scripts/               # 脚本工具
│   ├── setup/             # 安装配置脚本
│   └── benchmark/         # 基准测试脚本
├── docs/                  # 文档
├── tests/                 # 测试文件
└── README.md              # 项目说明
```

## 🛠️ 快速开始

### 1. 环境准备

```bash
# 安装依赖
pip install -r requirements.txt

# 设置环境变量
export REPO_PATH="/path/to/your/repository"
export PERSIST_PATH="./data/indexes"
```

### 2. 启动本地模型

确保您的本地模型服务正在运行：
- Embedding模型: `text-embedding-qwen3-embedding-0.6b`
- LLM模型: `qwen3-30b-a3b-mlx@4bit`
- API端点: `http://localhost:11434`

### 3. 构建索引

```bash
# 运行索引构建脚本
python scripts/setup/build_indexes.py --repo-path /path/to/repo
```

### 4. 生成评测数据

```bash
# 生成JSONL格式的评测数据集
python evaluation/datasets/generate_dataset.py \
    --repo-path /path/to/repo \
    --output evaluation_dataset.jsonl \
    --num-queries 20
```

### 5. 运行评测

```bash
# 执行完整评测
python scripts/benchmark/run_evaluation.py \
    --dataset evaluation_dataset.jsonl \
    --repo-path /path/to/repo
```

## 📊 评测指标

### 基础指标
- **Precision@K**: 前K个结果中相关结果的比例
- **Recall@K**: 前K个结果覆盖的相关结果比例
- **F1@K**: Precision和Recall的调和平均

### 排序指标
- **NDCG@K**: 考虑位置权重的排序质量指标
- **MAP**: 平均精确率
- **MRR**: 平均倒数排名

## 🔧 配置说明

### 模型配置
```python
# models/adapters/local_config.py
EMBEDDING_MODEL = "text-embedding-qwen3-embedding-0.6b"
LLM_MODEL = "qwen3-30b-a3b-mlx@4bit"
API_BASE_URL = "http://localhost:11434"
```

### 检索配置
```python
# core/utils/config.py
RETRIEVAL_CONFIG = {
    "bm25_top_k": 10,
    "embedding_top_k": 10,
    "final_top_k": 5,
    "enable_rerank": True
}
```

## 📖 详细文档

- [核心模块文档](core/README.md)
- [评测系统文档](evaluation/README.md)
- [模型适配文档](models/README.md)
- [API接口文档](docs/api.md)

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支: `git checkout -b feature/new-feature`
3. 提交更改: `git commit -am 'Add new feature'`
4. 推送分支: `git push origin feature/new-feature`
5. 创建Pull Request

## 📄 许可证

MIT License

## 🔗 相关链接

- [Model Context Protocol](https://modelcontextprotocol.io/)
- [LM Studio](https://lmstudio.ai/)
- [Qwen Models](https://github.com/QwenLM/Qwen)
