# Core - 核心功能模块

核心功能模块包含代码库问答系统的主要组件：检索器、管道和工具函数。

## 📁 模块结构

```
core/
├── retrieval/             # 检索器实现
│   ├── __init__.py
│   ├── bm25_retriever.py     # BM25检索器
│   ├── embedding_retriever.py # Embedding检索器
│   ├── graph_retriever.py     # 图检索器
│   └── base_retriever.py      # 检索器基类
├── pipeline/              # 检索管道
│   ├── __init__.py
│   ├── base_pipeline.py      # 管道基类
│   ├── simple_pipeline.py    # 简单管道
│   ├── pro_pipeline.py       # 专业管道
│   ├── balance_pipeline.py   # 平衡管道
│   └── best_pipeline.py      # 最佳管道
├── utils/                 # 工具函数
│   ├── __init__.py
│   ├── config.py             # 配置管理
│   ├── logger.py             # 日志工具
│   ├── result_processor.py   # 结果处理
│   └── file_utils.py         # 文件工具
└── README.md
```

## 🔍 检索器 (Retrieval)

### BM25检索器
基于词频-逆文档频率的传统检索方法，适合精确匹配查询。

**特点:**
- 快速响应
- 关键词匹配准确
- 适合代码符号搜索

**使用示例:**
```python
from core.retrieval import BM25Retriever

retriever = BM25Retriever(index_path="./data/indexes/bm25")
results = retriever.retrieve("HTTP GET request", top_k=10)
```

### Embedding检索器
基于语义向量的检索方法，支持自然语言理解。

**特点:**
- 语义理解能力强
- 支持模糊匹配
- 适合概念性查询

**使用示例:**
```python
from core.retrieval import EmbeddingRetriever

retriever = EmbeddingRetriever(
    index_path="./data/indexes/embedding",
    model_name="text-embedding-qwen3-embedding-0.6b"
)
results = retriever.retrieve("如何处理HTTP请求", top_k=10)
```

### 图检索器
基于代码结构图的检索方法，理解代码间的关系。

**特点:**
- 理解代码结构
- 支持关系查询
- 适合架构分析

## 🔄 管道 (Pipeline)

### 管道类型对比

| 管道类型 | 检索器组合 | 重排序 | 适用场景 | 性能 |
|---------|-----------|--------|----------|------|
| Simple | BM25 + Embedding | ❌ | 快速查询 | ⭐⭐⭐⭐⭐ |
| Pro | BM25 + Embedding + Graph | ❌ | 全面检索 | ⭐⭐⭐⭐ |
| Balance | BM25 + Embedding | ✅ | 平衡效果 | ⭐⭐⭐ |
| Best | BM25 + Embedding + Graph | ✅ | 最佳效果 | ⭐⭐ |

### 管道使用示例

```python
from core.pipeline import SimplePipeline, ProPipeline

# 简单管道
simple_pipeline = SimplePipeline(
    bm25_index_path="./data/indexes/bm25",
    embedding_index_path="./data/indexes/embedding"
)

# 专业管道
pro_pipeline = ProPipeline(
    bm25_index_path="./data/indexes/bm25",
    embedding_index_path="./data/indexes/embedding",
    graph_index_path="./data/indexes/graph"
)

# 执行查询
results = pro_pipeline.run("如何实现用户认证", top_k=5)
```

## 🛠️ 工具函数 (Utils)

### 配置管理
统一的配置管理系统，支持环境变量和配置文件。

```python
from core.utils import Config

config = Config()
print(config.retrieval.bm25_top_k)  # 10
print(config.models.embedding_model)  # text-embedding-qwen3-embedding-0.6b
```

### 结果处理
标准化的结果处理和格式转换。

```python
from core.utils import ResultProcessor, ResponseScheme

processor = ResultProcessor()
results = processor.process_results(raw_results, source="bm25")
deduplicated = processor.deduplicate_results(results)
```

### 日志系统
统一的日志配置和管理。

```python
from core.utils import setup_logger

logger = setup_logger(__name__)
logger.info("开始检索...")
```

## 🔧 扩展开发

### 添加新的检索器

1. 继承`BaseRetriever`类
2. 实现`build_index`和`retrieve`方法
3. 在`__init__.py`中注册

```python
from core.retrieval.base_retriever import BaseRetriever

class CustomRetriever(BaseRetriever):
    def build_index(self, documents):
        # 实现索引构建逻辑
        pass
    
    def retrieve(self, query, top_k=10):
        # 实现检索逻辑
        pass
```

### 添加新的管道

1. 继承`BasePipeline`类
2. 实现`run`方法
3. 配置检索器组合

```python
from core.pipeline.base_pipeline import BasePipeline

class CustomPipeline(BasePipeline):
    def run(self, query, top_k=5):
        # 实现自定义检索逻辑
        pass
```

## 📊 性能优化

### 索引优化
- 使用适当的分块大小
- 定期重建索引
- 启用索引压缩

### 检索优化
- 调整top_k参数
- 使用结果缓存
- 并行检索处理

### 内存优化
- 延迟加载索引
- 使用内存映射
- 定期清理缓存

## 🧪 测试

```bash
# 运行核心模块测试
python -m pytest tests/core/ -v

# 测试特定检索器
python -m pytest tests/core/test_bm25_retriever.py -v

# 性能测试
python tests/core/benchmark_retrievers.py
```
