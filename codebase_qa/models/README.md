# Models - 模型适配器

模型适配器模块，支持本地模型和云端模型的统一接口。

## 📁 模块结构

```
models/
├── local/                 # 本地模型
│   ├── __init__.py
│   ├── embedding.py          # 本地Embedding模型
│   ├── llm.py               # 本地LLM模型
│   └── config.py            # 本地模型配置
├── adapters/              # 模型适配器
│   ├── __init__.py
│   ├── base_adapter.py       # 基础适配器
│   ├── openai_adapter.py     # OpenAI适配器
│   ├── dashscope_adapter.py  # DashScope适配器
│   └── local_adapter.py      # 本地模型适配器
└── README.md
```

## 🏠 本地模型支持

### 支持的模型类型
- **Embedding模型**: `text-embedding-qwen3-embedding-0.6b`
- **LLM模型**: `qwen3-30b-a3b-mlx@4bit`
- **API服务**: LM Studio (localhost:11434)

### 配置示例

```python
# models/local/config.py
LOCAL_MODEL_CONFIG = {
    "api_base": "http://localhost:11434",
    "embedding_model": "text-embedding-qwen3-embedding-0.6b",
    "llm_model": "qwen3-30b-a3b-mlx@4bit",
    "timeout": 30,
    "max_retries": 3
}
```

### 使用示例

```python
from models.local import LocalEmbeddingModel, LocalLLMClient

# Embedding模型
embedding_model = LocalEmbeddingModel()
embeddings = embedding_model.encode(["测试文本1", "测试文本2"])

# LLM模型
llm_client = LocalLLMClient()
response = llm_client.chat_completion([
    {"role": "user", "content": "你好"}
])
```

## 🔌 模型适配器

### 统一接口
所有模型适配器都实现相同的接口：

```python
from models.adapters import BaseAdapter

class CustomAdapter(BaseAdapter):
    def encode_text(self, texts: List[str]) -> np.ndarray:
        """文本编码"""
        pass
    
    def chat_completion(self, messages: List[Dict]) -> str:
        """聊天完成"""
        pass
    
    def rerank(self, query: str, documents: List[str]) -> List[Tuple[int, float]]:
        """重排序"""
        pass
```

### 自动降级
当主要模型不可用时，自动降级到备用模型：

```python
from models.adapters import LocalAdapter

adapter = LocalAdapter(
    primary_model="text-embedding-qwen3-embedding-0.6b",
    fallback_model="sentence-transformers/all-MiniLM-L6-v2"
)

# 自动处理模型切换
embeddings = adapter.encode_text(texts)
```

## 🔧 配置管理

### 环境变量
```bash
# 本地模型配置
export LOCAL_API_BASE="http://localhost:11434"
export LOCAL_EMBEDDING_MODEL="text-embedding-qwen3-embedding-0.6b"
export LOCAL_LLM_MODEL="qwen3-30b-a3b-mlx@4bit"

# 云端模型配置（可选）
export OPENAI_API_KEY="your_openai_key"
export DASHSCOPE_API_KEY="your_dashscope_key"
```

### 配置文件
```yaml
# models/config.yaml
models:
  local:
    api_base: "http://localhost:11434"
    embedding_model: "text-embedding-qwen3-embedding-0.6b"
    llm_model: "qwen3-30b-a3b-mlx@4bit"
    timeout: 30
  
  openai:
    api_key: "${OPENAI_API_KEY}"
    embedding_model: "text-embedding-ada-002"
    llm_model: "gpt-3.5-turbo"
  
  dashscope:
    api_key: "${DASHSCOPE_API_KEY}"
    embedding_model: "text-embedding-v1"
    llm_model: "qwen-turbo"
```

## 🚀 快速开始

### 1. 启动本地模型服务

确保LM Studio正在运行并加载了相应模型：
- 访问 http://localhost:11434/v1/models 检查可用模型
- 确认embedding和LLM模型都已加载

### 2. 测试连接

```python
from models.local import test_local_models

# 测试所有本地模型
test_local_models()
```

### 3. 在项目中使用

```python
from models.adapters import LocalAdapter

# 初始化适配器
adapter = LocalAdapter()

# 在检索器中使用
from core.retrieval import EmbeddingRetriever

retriever = EmbeddingRetriever(
    index_path="./data/indexes/embedding",
    model_adapter=adapter
)
```

## 🔍 故障排除

### 常见问题

1. **连接失败**
   ```
   错误: Connection refused
   解决: 检查LM Studio是否正在运行
   ```

2. **模型未找到**
   ```
   错误: Model not found
   解决: 确认模型名称正确，检查已加载的模型列表
   ```

3. **超时错误**
   ```
   错误: Request timeout
   解决: 增加timeout设置，检查模型响应速度
   ```

### 调试模式

```python
import logging
logging.basicConfig(level=logging.DEBUG)

from models.local import LocalEmbeddingModel

model = LocalEmbeddingModel()
model.encode(["测试"], debug=True)
```

## 📊 性能优化

### 批量处理
```python
# 批量编码以提高效率
texts = ["文本1", "文本2", "文本3"]
embeddings = embedding_model.encode(texts, batch_size=32)
```

### 缓存机制
```python
from models.adapters import CachedAdapter

adapter = CachedAdapter(
    base_adapter=LocalAdapter(),
    cache_size=1000,
    cache_ttl=3600  # 1小时
)
```

### 异步处理
```python
import asyncio
from models.local import AsyncLocalLLMClient

async def async_chat():
    client = AsyncLocalLLMClient()
    response = await client.chat_completion_async(messages)
    return response
```

## 🧪 测试

```bash
# 测试本地模型连接
python models/local/test_connection.py

# 测试适配器功能
python -m pytest tests/models/ -v

# 性能基准测试
python tests/models/benchmark_models.py
```
