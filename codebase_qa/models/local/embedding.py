"""
本地Embedding模型适配器
支持text-embedding-qwen3-embedding-0.6b模型
"""
import requests
import json
import numpy as np
from typing import List, Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)


class LocalEmbeddingModel:
    """本地Embedding模型适配器"""
    
    def __init__(
        self, 
        base_url: str = "http://localhost:11434", 
        model_name: str = "text-embedding-qwen3-embedding-0.6b",
        timeout: int = 30,
        max_retries: int = 3
    ):
        self.base_url = base_url
        self.model_name = model_name
        self.timeout = timeout
        self.max_retries = max_retries
        self.embeddings_url = f"{base_url}/v1/embeddings"
        
        # 初始化降级模型
        self._fallback_model = None
        self._use_fallback = False
    
    def _init_fallback_model(self):
        """初始化降级模型"""
        try:
            from sentence_transformers import SentenceTransformer
            self._fallback_model = SentenceTransformer('all-MiniLM-L6-v2')
            logger.info("降级模型初始化成功: all-MiniLM-L6-v2")
        except ImportError:
            logger.warning("sentence-transformers未安装，无法使用降级模型")
        except Exception as e:
            logger.warning(f"降级模型初始化失败: {e}")
    
    def encode(self, texts: List[str], **kwargs) -> np.ndarray:
        """编码文本为向量"""
        if isinstance(texts, str):
            texts = [texts]
        
        # 如果已经标记使用降级模型，直接使用
        if self._use_fallback:
            return self._encode_with_fallback(texts, **kwargs)
        
        # 尝试使用本地API
        for attempt in range(self.max_retries):
            try:
                response = requests.post(
                    self.embeddings_url,
                    json={
                        "model": self.model_name,
                        "input": texts
                    },
                    timeout=self.timeout
                )
                
                if response.status_code == 200:
                    result = response.json()
                    embeddings = []
                    
                    for item in result["data"]:
                        embeddings.append(item["embedding"])
                    
                    logger.debug(f"本地API编码成功，文本数量: {len(texts)}")
                    return np.array(embeddings)
                else:
                    logger.warning(f"本地API返回错误状态码: {response.status_code}")
                    
            except requests.exceptions.RequestException as e:
                logger.warning(f"本地API请求失败 (尝试 {attempt + 1}/{self.max_retries}): {e}")
                if attempt == self.max_retries - 1:
                    # 最后一次尝试失败，切换到降级模式
                    logger.info("切换到降级模型")
                    self._use_fallback = True
                    return self._encode_with_fallback(texts, **kwargs)
            except Exception as e:
                logger.error(f"本地API调用异常: {e}")
                break
        
        # 如果所有尝试都失败，使用降级模型
        return self._encode_with_fallback(texts, **kwargs)
    
    def _encode_with_fallback(self, texts: List[str], **kwargs) -> np.ndarray:
        """使用降级模型编码"""
        if self._fallback_model is None:
            self._init_fallback_model()
        
        if self._fallback_model is not None:
            try:
                embeddings = self._fallback_model.encode(texts, **kwargs)
                logger.debug(f"降级模型编码成功，文本数量: {len(texts)}")
                return embeddings
            except Exception as e:
                logger.error(f"降级模型编码失败: {e}")
                # 返回随机向量作为最后的降级方案
                return np.random.randn(len(texts), 384).astype(np.float32)
        else:
            logger.error("降级模型不可用，返回随机向量")
            return np.random.randn(len(texts), 384).astype(np.float32)
    
    def test_connection(self) -> bool:
        """测试连接"""
        try:
            test_texts = ["测试连接"]
            embeddings = self.encode(test_texts)
            return embeddings is not None and len(embeddings) > 0
        except Exception as e:
            logger.error(f"连接测试失败: {e}")
            return False
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        try:
            response = requests.get(f"{self.base_url}/v1/models", timeout=10)
            if response.status_code == 200:
                models = response.json()
                for model in models.get("data", []):
                    if model.get("id") == self.model_name:
                        return model
            return {"error": "模型未找到"}
        except Exception as e:
            return {"error": str(e)}


def test_local_embedding_model():
    """测试本地embedding模型"""
    print("🧪 测试本地Embedding模型...")
    
    model = LocalEmbeddingModel()
    
    # 测试连接
    if model.test_connection():
        print("✅ 连接测试成功")
    else:
        print("❌ 连接测试失败")
    
    # 测试编码
    test_texts = [
        "这是一个测试文本",
        "HTTP GET request implementation",
        "如何处理用户认证"
    ]
    
    try:
        embeddings = model.encode(test_texts)
        print(f"✅ 编码测试成功")
        print(f"   文本数量: {len(test_texts)}")
        print(f"   向量维度: {embeddings.shape}")
        print(f"   向量类型: {embeddings.dtype}")
        
        # 测试相似度计算
        similarity = np.dot(embeddings[0], embeddings[1])
        print(f"   相似度示例: {similarity:.4f}")
        
    except Exception as e:
        print(f"❌ 编码测试失败: {e}")
    
    # 获取模型信息
    model_info = model.get_model_info()
    print(f"📋 模型信息: {model_info}")


if __name__ == "__main__":
    test_local_embedding_model()
