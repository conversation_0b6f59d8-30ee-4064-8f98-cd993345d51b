#!/usr/bin/env python3
"""
完整的评测流程脚本
1. 生成JSONL格式的评测数据集
2. 运行本地评测
3. 生成详细报告
"""
import os
import sys
import argparse
import subprocess
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))


def generate_dataset(repo_path: str, output_path: str) -> bool:
    """生成评测数据集"""
    print("📊 生成评测数据集...")
    
    try:
        # 导入生成器
        from evaluation.datasets.generator import main as generate_main
        
        # 切换到正确的目录
        original_cwd = os.getcwd()
        os.chdir(project_root / "evaluation" / "datasets")
        
        try:
            generate_main()
            
            # 移动生成的文件到指定位置
            generated_file = "requests_evaluation_dataset.jsonl"
            if os.path.exists(generated_file):
                import shutil
                shutil.move(generated_file, output_path)
                print(f"✅ 数据集生成成功: {output_path}")
                return True
            else:
                print("❌ 数据集文件未生成")
                return False
                
        finally:
            os.chdir(original_cwd)
            
    except Exception as e:
        print(f"❌ 数据集生成失败: {e}")
        return False


def run_evaluation(repo_path: str, dataset_path: str, output_path: str) -> bool:
    """运行评测"""
    print("🚀 运行评测...")
    
    try:
        # 导入评测执行器
        from evaluation.runners.local_runner import LocalEvaluationRunner
        
        runner = LocalEvaluationRunner(repo_path, dataset_path)
        report = runner.run_evaluation()
        
        # 保存报告
        import json
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 评测完成: {output_path}")
        return True
        
    except Exception as e:
        print(f"❌ 评测失败: {e}")
        return False


def display_results(report_path: str):
    """显示评测结果"""
    try:
        import json
        with open(report_path, 'r', encoding='utf-8') as f:
            report = json.load(f)
        
        print("\n" + "="*60)
        print("🎉 评测结果汇总")
        print("="*60)
        
        summary = report["summary"]
        print(f"📊 总体统计:")
        print(f"   总查询数: {summary['total_queries']}")
        print(f"   成功查询: {summary['successful_queries']}")
        print(f"   失败查询: {summary['failed_queries']}")
        print(f"   成功率: {summary['success_rate']:.1%}")
        print(f"   总耗时: {summary['total_time']:.2f}秒")
        print(f"   平均耗时: {summary['avg_time_per_query']:.2f}秒/查询")
        
        if report["aggregated_metrics"]:
            print(f"\n📈 关键指标 (平均值):")
            metrics = report["aggregated_metrics"]
            key_metrics = ["precision@1", "precision@3", "precision@5", "recall@5", "f1@5", "hit_rate@5"]
            
            for metric in key_metrics:
                if metric in metrics:
                    value = metrics[metric]["mean"]
                    print(f"   {metric}: {value:.4f}")
        
        # 显示最佳和最差查询
        successful_results = [r for r in report["detailed_results"] if r["success"]]
        if successful_results:
            # 按precision@5排序
            successful_results.sort(key=lambda x: x["metrics"]["precision@5"], reverse=True)
            
            print(f"\n🏆 表现最好的查询 (Top 3):")
            for i, result in enumerate(successful_results[:3], 1):
                p5 = result["metrics"]["precision@5"]
                print(f"   {i}. {result['query_id']}: {result['query_text'][:50]}... (P@5: {p5:.3f})")
            
            print(f"\n⚠️  表现最差的查询 (Bottom 3):")
            for i, result in enumerate(successful_results[-3:], 1):
                p5 = result["metrics"]["precision@5"]
                print(f"   {i}. {result['query_id']}: {result['query_text'][:50]}... (P@5: {p5:.3f})")
        
        print(f"\n📁 详细报告: {report_path}")
        
    except Exception as e:
        print(f"❌ 结果显示失败: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="运行完整的代码库评测流程")
    
    # 必需参数
    parser.add_argument("--repo-path", required=True, help="代码库路径")
    
    # 可选参数
    parser.add_argument("--output-dir", default="./evaluation_results", help="输出目录")
    parser.add_argument("--dataset-name", default="evaluation_dataset.jsonl", help="数据集文件名")
    parser.add_argument("--report-name", default="evaluation_report.json", help="报告文件名")
    parser.add_argument("--skip-dataset", action="store_true", help="跳过数据集生成")
    parser.add_argument("--dataset-path", help="现有数据集路径（如果跳过生成）")
    
    args = parser.parse_args()
    
    # 验证参数
    if not os.path.exists(args.repo_path):
        print(f"❌ 代码库路径不存在: {args.repo_path}")
        sys.exit(1)
    
    if args.skip_dataset and not args.dataset_path:
        print("❌ 跳过数据集生成时必须提供现有数据集路径")
        sys.exit(1)
    
    if args.skip_dataset and not os.path.exists(args.dataset_path):
        print(f"❌ 数据集文件不存在: {args.dataset_path}")
        sys.exit(1)
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 设置文件路径
    dataset_path = args.dataset_path if args.skip_dataset else os.path.join(args.output_dir, args.dataset_name)
    report_path = os.path.join(args.output_dir, args.report_name)
    
    print("🚀 开始完整评测流程")
    print(f"📁 代码库路径: {args.repo_path}")
    print(f"📁 输出目录: {args.output_dir}")
    print(f"📊 数据集路径: {dataset_path}")
    print(f"📋 报告路径: {report_path}")
    
    success = True
    
    # Step 1: 生成数据集（如果需要）
    if not args.skip_dataset:
        if not generate_dataset(args.repo_path, dataset_path):
            success = False
    else:
        print(f"⏭️  跳过数据集生成，使用现有数据集: {dataset_path}")
    
    # Step 2: 运行评测
    if success:
        if not run_evaluation(args.repo_path, dataset_path, report_path):
            success = False
    
    # Step 3: 显示结果
    if success:
        display_results(report_path)
        print("\n🎉 完整评测流程成功完成！")
    else:
        print("\n❌ 评测流程失败")
        sys.exit(1)


if __name__ == "__main__":
    main()
