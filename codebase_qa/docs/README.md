# 项目文档

## 📚 文档目录

### 核心文档
- [项目概览](../README.md) - 项目整体介绍
- [快速开始指南](quick_start.md) - 5分钟上手指南
- [API文档](api.md) - 详细的API接口文档

### 模块文档
- [核心模块](../core/README.md) - 检索器和管道文档
- [评测系统](../evaluation/README.md) - 评测框架文档
- [模型适配器](../models/README.md) - 本地模型适配文档

### 开发文档
- [开发指南](development.md) - 开发环境搭建和贡献指南
- [架构设计](architecture.md) - 系统架构和设计理念
- [部署指南](deployment.md) - 生产环境部署指南

### 示例和教程
- [使用示例](examples.md) - 详细的使用示例
- [最佳实践](best_practices.md) - 推荐的使用方式
- [故障排除](troubleshooting.md) - 常见问题解决方案

## 🔗 外部资源

- [Model Context Protocol](https://modelcontextprotocol.io/)
- [LM Studio](https://lmstudio.ai/)
- [Qwen Models](https://github.com/QwenLM/Qwen)
