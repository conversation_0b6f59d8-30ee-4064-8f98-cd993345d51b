# Evaluation - 评测系统

完整的代码库问答系统评测框架，支持数据集生成、指标计算和自动化评测。

## 📁 模块结构

```
evaluation/
├── datasets/              # 数据集管理
│   ├── __init__.py
│   ├── generator.py          # 数据集生成器
│   ├── schema.py             # 数据格式定义
│   ├── predefined.py         # 预定义数据集
│   └── validator.py          # 数据验证器
├── metrics/               # 指标计算
│   ├── __init__.py
│   ├── calculator.py         # 指标计算器
│   ├── aggregator.py         # 指标聚合器
│   └── visualizer.py         # 结果可视化
├── runners/               # 评测执行器
│   ├── __init__.py
│   ├── base_runner.py        # 基础执行器
│   ├── local_runner.py       # 本地评测执行器
│   └── batch_runner.py       # 批量评测执行器
└── README.md
```

## 📊 数据集格式 (JSONL)

评测数据集采用JSONL格式，每行一个JSON对象：

```jsonl
{"id": "req_001", "query": "如何发送HTTP GET请求", "ground_truth": [{"file_path": "src/requests/api.py", "start_line": 1, "end_line": 50, "relevance_score": 1.0, "explanation": "包含get函数的定义和实现"}]}
{"id": "req_002", "query": "如何处理HTTP响应", "ground_truth": [{"file_path": "src/requests/models.py", "start_line": 1, "end_line": 200, "relevance_score": 1.0, "explanation": "Response类的完整定义"}]}
```

### 数据格式说明

每条评测数据包含：
- **id**: 唯一标识符
- **query**: 自然语言查询
- **ground_truth**: 期望找到的代码片段列表
  - **file_path**: 文件路径
  - **start_line**: 起始行号（可选）
  - **end_line**: 结束行号（可选）
  - **relevance_score**: 相关性分数 (0.0-1.0)
  - **explanation**: 相关性解释

## 🔧 数据集生成

### 自动生成
基于代码库结构自动生成评测数据：

```python
from evaluation.datasets import DatasetGenerator

generator = DatasetGenerator(repo_path="/path/to/repo")
dataset = generator.generate_jsonl(
    output_path="evaluation_dataset.jsonl",
    num_queries=20
)
```

### 手工定制
创建高质量的手工标注数据：

```python
from evaluation.datasets import create_custom_dataset

queries = [
    {
        "id": "custom_001",
        "query": "如何实现用户认证",
        "ground_truth": [
            {
                "file_path": "src/auth/authentication.py",
                "start_line": 10,
                "end_line": 50,
                "relevance_score": 1.0,
                "explanation": "主要的用户认证实现"
            }
        ]
    }
]

create_custom_dataset(queries, "custom_dataset.jsonl")
```

### 预定义数据集
针对特定领域的专业数据集：

```python
from evaluation.datasets import (
    create_web_dev_dataset,
    create_ml_dataset,
    create_general_dataset
)

# Web开发数据集
web_dataset = create_web_dev_dataset("/path/to/web/repo")

# 机器学习数据集  
ml_dataset = create_ml_dataset("/path/to/ml/repo")
```

## 📈 评测指标

### 基础指标
- **Precision@K**: 前K个结果中相关结果的比例
- **Recall@K**: 前K个结果覆盖的相关结果比例
- **F1@K**: Precision和Recall的调和平均

### 排序指标
- **NDCG@K**: 归一化折损累积增益
- **MAP**: 平均精确率
- **MRR**: 平均倒数排名

### 覆盖指标
- **Hit Rate@K**: 前K个结果中是否包含相关结果
- **Coverage**: 所有相关文档中被检索到的比例

### 使用示例

```python
from evaluation.metrics import MetricsCalculator

calculator = MetricsCalculator()

# 计算单个查询的指标
metrics = calculator.calculate_all_metrics(
    retrieved_results=search_results,
    ground_truth=expected_results,
    k_values=[1, 3, 5, 10]
)

print(f"Precision@5: {metrics['precision@5'].value:.3f}")
print(f"NDCG@5: {metrics['ndcg@5'].value:.3f}")
```

## 🚀 运行评测

### 本地评测
使用本地模型运行评测：

```python
from evaluation.runners import LocalRunner

runner = LocalRunner(
    dataset_path="evaluation_dataset.jsonl",
    repo_path="/path/to/repo",
    model_config={
        "embedding_model": "text-embedding-qwen3-embedding-0.6b",
        "llm_model": "qwen3-30b-a3b-mlx@4bit",
        "api_base": "http://localhost:11434"
    }
)

results = runner.run_evaluation()
```

### 批量评测
对比多个Pipeline的性能：

```python
from evaluation.runners import BatchRunner

runner = BatchRunner(
    dataset_path="evaluation_dataset.jsonl",
    repo_path="/path/to/repo"
)

# 评测多个Pipeline
results = runner.run_batch_evaluation([
    "simple", "pro", "balance", "best"
])

# 生成对比报告
runner.generate_comparison_report(results)
```

## 📊 结果分析

### 指标聚合
```python
from evaluation.metrics import MetricsAggregator

aggregator = MetricsAggregator()
aggregated = aggregator.aggregate_metrics(all_query_results)

# 生成汇总报告
report = aggregator.create_summary_report(aggregated)
```

### 可视化
```python
from evaluation.metrics import MetricsVisualizer

visualizer = MetricsVisualizer()

# 生成性能对比图
visualizer.plot_pipeline_comparison(results)

# 生成指标分布图
visualizer.plot_metrics_distribution(aggregated_metrics)
```

## 🔧 配置选项

### 评测配置
```python
EVALUATION_CONFIG = {
    "k_values": [1, 3, 5, 10],
    "enable_rerank": True,
    "timeout_per_query": 30,
    "save_detailed_results": True,
    "output_format": "json"
}
```

### 数据集配置
```python
DATASET_CONFIG = {
    "chunk_size": 50,
    "overlap_lines": 10,
    "min_chunk_chars": 100,
    "max_queries_per_file": 5,
    "difficulty_distribution": {
        "easy": 0.3,
        "medium": 0.5, 
        "hard": 0.2
    }
}
```

## 🧪 测试和验证

### 数据集验证
```bash
# 验证JSONL格式
python evaluation/datasets/validator.py --input dataset.jsonl

# 检查数据质量
python evaluation/datasets/validator.py --input dataset.jsonl --check-quality
```

### 评测测试
```bash
# 运行评测测试
python -m pytest tests/evaluation/ -v

# 性能基准测试
python tests/evaluation/benchmark_metrics.py
```

## 📋 最佳实践

### 数据集设计
1. **多样性**: 包含不同类型和难度的查询
2. **平衡性**: 各个模块和功能的覆盖要均衡
3. **质量**: 确保ground truth的准确性
4. **规模**: 至少20-50个查询用于可靠评测

### 评测执行
1. **环境一致性**: 确保评测环境的一致性
2. **多次运行**: 进行多次评测取平均值
3. **错误处理**: 记录和分析失败的查询
4. **结果保存**: 保存详细的评测结果用于分析

### 结果分析
1. **多维度分析**: 从不同角度分析性能
2. **错误分析**: 深入分析失败案例
3. **趋势分析**: 跟踪性能变化趋势
4. **对比分析**: 与基线模型对比
