"""
本地评测执行器
使用本地模型运行评测
"""
import json
import time
import os
from typing import List, Dict, Any, Optional
from pathlib import Path

# 添加项目路径
import sys
sys.path.append(str(Path(__file__).parent.parent.parent))

from models.local.embedding import LocalEmbeddingModel


class SimpleRetriever:
    """简化的检索器，用于评测"""
    
    def __init__(self, repo_path: str):
        self.repo_path = repo_path
        self.documents = []
        self.metadata = []
        self.embedding_model = LocalEmbeddingModel()
        self._build_simple_index()
    
    def _build_simple_index(self):
        """构建简单索引"""
        print("📦 构建简单索引...")
        
        for root, dirs, files in os.walk(self.repo_path):
            # 跳过隐藏目录
            dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['__pycache__', 'node_modules']]
            
            for file in files:
                if file.endswith(('.py', '.js', '.java', '.cpp', '.c', '.h')):
                    file_path = os.path.join(root, file)
                    rel_path = os.path.relpath(file_path, self.repo_path)
                    
                    try:
                        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                            content = f.read()
                        
                        # 简单分块
                        lines = content.split('\n')
                        chunk_size = 50
                        
                        for i in range(0, len(lines), chunk_size):
                            chunk_lines = lines[i:i+chunk_size]
                            chunk_text = '\n'.join(chunk_lines)
                            
                            if chunk_text.strip():
                                self.documents.append(chunk_text)
                                self.metadata.append({
                                    "file_path": rel_path,
                                    "start_line": i + 1,
                                    "end_line": min(i + chunk_size, len(lines))
                                })
                    
                    except Exception as e:
                        print(f"⚠️  读取文件失败 {rel_path}: {e}")
                        continue
        
        print(f"✅ 索引构建完成，文档数量: {len(self.documents)}")
    
    def retrieve(self, query: str, top_k: int = 10) -> List[Dict[str, Any]]:
        """简单检索"""
        if not self.documents:
            return []
        
        try:
            # 使用关键词匹配作为简单检索
            query_lower = query.lower()
            results = []
            
            for i, (doc, meta) in enumerate(zip(self.documents, self.metadata)):
                doc_lower = doc.lower()
                
                # 简单的关键词匹配评分
                score = 0.0
                query_words = query_lower.split()
                
                for word in query_words:
                    if word in doc_lower:
                        score += doc_lower.count(word) / len(doc_lower)
                
                if score > 0:
                    results.append({
                        "text": doc,
                        "score": score,
                        "metadata": meta,
                        "file_path": meta["file_path"]
                    })
            
            # 按分数排序
            results.sort(key=lambda x: x["score"], reverse=True)
            return results[:top_k]
            
        except Exception as e:
            print(f"❌ 检索失败: {e}")
            return []


class LocalEvaluationRunner:
    """本地评测执行器"""
    
    def __init__(self, repo_path: str, dataset_path: str):
        self.repo_path = repo_path
        self.dataset_path = dataset_path
        self.retriever = SimpleRetriever(repo_path)
    
    def load_dataset(self) -> List[Dict[str, Any]]:
        """加载JSONL数据集"""
        queries = []
        try:
            with open(self.dataset_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    if line.strip():
                        try:
                            data = json.loads(line)
                            queries.append(data)
                        except json.JSONDecodeError as e:
                            print(f"⚠️  第{line_num}行JSON解析失败: {e}")
                            continue
            
            print(f"✅ 加载数据集成功，查询数量: {len(queries)}")
            return queries
            
        except Exception as e:
            print(f"❌ 加载数据集失败: {e}")
            return []
    
    def calculate_precision_recall(
        self, 
        retrieved_files: List[str], 
        ground_truth_files: List[str]
    ) -> tuple:
        """计算精确率和召回率"""
        if not retrieved_files:
            return 0.0, 0.0
        
        retrieved_set = set(retrieved_files)
        ground_truth_set = set(ground_truth_files)
        
        intersection = retrieved_set.intersection(ground_truth_set)
        
        precision = len(intersection) / len(retrieved_set) if retrieved_set else 0.0
        recall = len(intersection) / len(ground_truth_set) if ground_truth_set else 0.0
        
        return precision, recall
    
    def evaluate_single_query(self, query_data: Dict[str, Any]) -> Dict[str, Any]:
        """评测单个查询"""
        query_id = query_data["id"]
        query_text = query_data["query"]
        ground_truth = query_data["ground_truth"]
        
        try:
            # 执行检索
            start_time = time.time()
            results = self.retriever.retrieve(query_text, top_k=10)
            execution_time = time.time() - start_time
            
            # 提取检索到的文件路径
            retrieved_files = [r["file_path"] for r in results]
            ground_truth_files = [gt["file_path"] for gt in ground_truth]
            
            # 计算指标
            metrics = {}
            for k in [1, 3, 5, 10]:
                retrieved_k = retrieved_files[:k]
                precision, recall = self.calculate_precision_recall(retrieved_k, ground_truth_files)
                f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0.0
                
                metrics[f"precision@{k}"] = precision
                metrics[f"recall@{k}"] = recall
                metrics[f"f1@{k}"] = f1
            
            # 计算Hit Rate@5
            hit_rate_5 = 1.0 if any(f in ground_truth_files for f in retrieved_files[:5]) else 0.0
            metrics["hit_rate@5"] = hit_rate_5
            
            return {
                "query_id": query_id,
                "query_text": query_text,
                "execution_time": execution_time,
                "metrics": metrics,
                "retrieved_files": retrieved_files[:5],
                "ground_truth_files": ground_truth_files,
                "success": True
            }
            
        except Exception as e:
            return {
                "query_id": query_id,
                "query_text": query_text,
                "error": str(e),
                "success": False
            }
    
    def run_evaluation(self) -> Dict[str, Any]:
        """运行完整评测"""
        print("🚀 开始本地评测")
        
        # 加载数据集
        queries = self.load_dataset()
        if not queries:
            return {"error": "数据集加载失败"}
        
        # 执行评测
        results = []
        successful_count = 0
        failed_count = 0
        
        start_time = time.time()
        
        for i, query_data in enumerate(queries):
            print(f"📝 [{i+1}/{len(queries)}] 评测查询: {query_data['id']}")
            
            result = self.evaluate_single_query(query_data)
            results.append(result)
            
            if result["success"]:
                successful_count += 1
                # 显示关键指标
                metrics = result["metrics"]
                p5 = metrics.get("precision@5", 0)
                r5 = metrics.get("recall@5", 0)
                hr5 = metrics.get("hit_rate@5", 0)
                print(f"   ✅ P@5: {p5:.3f}, R@5: {r5:.3f}, HR@5: {hr5:.3f}")
            else:
                failed_count += 1
                print(f"   ❌ 失败: {result.get('error', '未知错误')}")
        
        total_time = time.time() - start_time
        
        # 计算聚合指标
        successful_results = [r for r in results if r["success"]]
        aggregated_metrics = {}
        
        if successful_results:
            metric_names = successful_results[0]["metrics"].keys()
            for metric_name in metric_names:
                values = [r["metrics"][metric_name] for r in successful_results]
                aggregated_metrics[metric_name] = {
                    "mean": sum(values) / len(values),
                    "min": min(values),
                    "max": max(values),
                    "count": len(values)
                }
        
        # 生成报告
        report = {
            "summary": {
                "total_queries": len(queries),
                "successful_queries": successful_count,
                "failed_queries": failed_count,
                "success_rate": successful_count / len(queries) if queries else 0,
                "total_time": total_time,
                "avg_time_per_query": total_time / len(queries) if queries else 0
            },
            "aggregated_metrics": aggregated_metrics,
            "detailed_results": results,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        
        return report


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="运行本地评测")
    parser.add_argument("--repo-path", required=True, help="代码库路径")
    parser.add_argument("--dataset", required=True, help="JSONL数据集路径")
    parser.add_argument("--output", default="evaluation_report.json", help="输出报告路径")
    
    args = parser.parse_args()
    
    # 验证路径
    if not os.path.exists(args.repo_path):
        print(f"❌ 代码库路径不存在: {args.repo_path}")
        return
    
    if not os.path.exists(args.dataset):
        print(f"❌ 数据集文件不存在: {args.dataset}")
        return
    
    # 运行评测
    runner = LocalEvaluationRunner(args.repo_path, args.dataset)
    report = runner.run_evaluation()
    
    # 保存报告
    with open(args.output, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    # 显示结果
    print("\n" + "="*60)
    print("🎉 评测完成！")
    print("="*60)
    
    summary = report["summary"]
    print(f"📊 总体统计:")
    print(f"   总查询数: {summary['total_queries']}")
    print(f"   成功查询: {summary['successful_queries']}")
    print(f"   失败查询: {summary['failed_queries']}")
    print(f"   成功率: {summary['success_rate']:.1%}")
    print(f"   总耗时: {summary['total_time']:.2f}秒")
    
    if report["aggregated_metrics"]:
        print(f"\n📈 关键指标 (平均值):")
        metrics = report["aggregated_metrics"]
        for metric in ["precision@5", "recall@5", "f1@5", "hit_rate@5"]:
            if metric in metrics:
                value = metrics[metric]["mean"]
                print(f"   {metric}: {value:.4f}")
    
    print(f"\n📁 详细报告: {args.output}")


if __name__ == "__main__":
    main()
