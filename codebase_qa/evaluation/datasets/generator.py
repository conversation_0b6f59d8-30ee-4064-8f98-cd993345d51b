"""
评测数据集生成器
生成标准JSONL格式的评测数据集
"""
import json
import os
from typing import List, Dict, Any


def create_requests_evaluation_dataset() -> List[Dict[str, Any]]:
    """创建requests库的评测数据集"""
    
    # 精心设计的20条评测语料
    queries = [
        {
            "id": "req_001",
            "query": "如何发送HTTP GET请求",
            "ground_truth": [
                {
                    "file_path": "src/requests/api.py",
                    "start_line": 1,
                    "end_line": 50,
                    "relevance_score": 1.0,
                    "explanation": "包含get函数的定义和实现"
                }
            ]
        },
        {
            "id": "req_002", 
            "query": "如何处理HTTP响应",
            "ground_truth": [
                {
                    "file_path": "src/requests/models.py",
                    "start_line": 1,
                    "end_line": 200,
                    "relevance_score": 1.0,
                    "explanation": "Response类的完整定义"
                }
            ]
        },
        {
            "id": "req_003",
            "query": "Session会话管理的实现",
            "ground_truth": [
                {
                    "file_path": "src/requests/sessions.py",
                    "start_line": 1,
                    "end_line": 500,
                    "relevance_score": 1.0,
                    "explanation": "Session类的完整实现"
                }
            ]
        },
        {
            "id": "req_004",
            "query": "HTTP适配器的作用",
            "ground_truth": [
                {
                    "file_path": "src/requests/adapters.py",
                    "start_line": 1,
                    "end_line": 300,
                    "relevance_score": 1.0,
                    "explanation": "HTTPAdapter类的实现"
                }
            ]
        },
        {
            "id": "req_005",
            "query": "如何处理认证",
            "ground_truth": [
                {
                    "file_path": "src/requests/auth.py",
                    "start_line": 1,
                    "end_line": 200,
                    "relevance_score": 1.0,
                    "explanation": "认证相关类的实现"
                }
            ]
        },
        {
            "id": "req_006",
            "query": "Cookie处理机制",
            "ground_truth": [
                {
                    "file_path": "src/requests/cookies.py",
                    "start_line": 1,
                    "end_line": 400,
                    "relevance_score": 1.0,
                    "explanation": "Cookie处理的完整实现"
                }
            ]
        },
        {
            "id": "req_007",
            "query": "异常处理和错误类型",
            "ground_truth": [
                {
                    "file_path": "src/requests/exceptions.py",
                    "start_line": 1,
                    "end_line": 150,
                    "relevance_score": 1.0,
                    "explanation": "所有异常类的定义"
                }
            ]
        },
        {
            "id": "req_008",
            "query": "如何发送POST请求",
            "ground_truth": [
                {
                    "file_path": "src/requests/api.py",
                    "start_line": 50,
                    "end_line": 100,
                    "relevance_score": 1.0,
                    "explanation": "post函数的定义"
                }
            ]
        },
        {
            "id": "req_009",
            "query": "工具函数和辅助方法",
            "ground_truth": [
                {
                    "file_path": "src/requests/utils.py",
                    "start_line": 1,
                    "end_line": 800,
                    "relevance_score": 1.0,
                    "explanation": "各种工具函数的实现"
                }
            ]
        },
        {
            "id": "req_010",
            "query": "HTTP状态码处理",
            "ground_truth": [
                {
                    "file_path": "src/requests/status_codes.py",
                    "start_line": 1,
                    "end_line": 100,
                    "relevance_score": 1.0,
                    "explanation": "状态码相关的定义"
                }
            ]
        },
        {
            "id": "req_011",
            "query": "数据结构和容器类",
            "ground_truth": [
                {
                    "file_path": "src/requests/structures.py",
                    "start_line": 1,
                    "end_line": 200,
                    "relevance_score": 1.0,
                    "explanation": "CaseInsensitiveDict等数据结构"
                }
            ]
        },
        {
            "id": "req_012",
            "query": "钩子函数机制",
            "ground_truth": [
                {
                    "file_path": "src/requests/hooks.py",
                    "start_line": 1,
                    "end_line": 50,
                    "relevance_score": 1.0,
                    "explanation": "钩子函数的实现"
                }
            ]
        },
        {
            "id": "req_013",
            "query": "兼容性处理代码",
            "ground_truth": [
                {
                    "file_path": "src/requests/compat.py",
                    "start_line": 1,
                    "end_line": 100,
                    "relevance_score": 1.0,
                    "explanation": "Python版本兼容性处理"
                }
            ]
        },
        {
            "id": "req_014",
            "query": "SSL证书处理",
            "ground_truth": [
                {
                    "file_path": "src/requests/certs.py",
                    "start_line": 1,
                    "end_line": 50,
                    "relevance_score": 1.0,
                    "explanation": "SSL证书相关功能"
                }
            ]
        },
        {
            "id": "req_015",
            "query": "如何设置请求头",
            "ground_truth": [
                {
                    "file_path": "src/requests/models.py",
                    "start_line": 200,
                    "end_line": 300,
                    "relevance_score": 0.9,
                    "explanation": "Request类中headers的处理"
                },
                {
                    "file_path": "src/requests/sessions.py",
                    "start_line": 300,
                    "end_line": 400,
                    "relevance_score": 0.8,
                    "explanation": "Session中headers的合并逻辑"
                }
            ]
        },
        {
            "id": "req_016",
            "query": "超时设置和处理",
            "ground_truth": [
                {
                    "file_path": "src/requests/adapters.py",
                    "start_line": 200,
                    "end_line": 300,
                    "relevance_score": 1.0,
                    "explanation": "适配器中的超时处理逻辑"
                }
            ]
        },
        {
            "id": "req_017",
            "query": "代理服务器配置",
            "ground_truth": [
                {
                    "file_path": "src/requests/adapters.py",
                    "start_line": 100,
                    "end_line": 200,
                    "relevance_score": 1.0,
                    "explanation": "代理配置相关代码"
                },
                {
                    "file_path": "src/requests/sessions.py",
                    "start_line": 400,
                    "end_line": 500,
                    "relevance_score": 0.7,
                    "explanation": "Session中的代理处理"
                }
            ]
        },
        {
            "id": "req_018",
            "query": "文件上传功能",
            "ground_truth": [
                {
                    "file_path": "src/requests/models.py",
                    "start_line": 300,
                    "end_line": 400,
                    "relevance_score": 1.0,
                    "explanation": "Request类中文件处理逻辑"
                }
            ]
        },
        {
            "id": "req_019",
            "query": "重定向处理机制",
            "ground_truth": [
                {
                    "file_path": "src/requests/sessions.py",
                    "start_line": 100,
                    "end_line": 200,
                    "relevance_score": 1.0,
                    "explanation": "Session中的重定向处理逻辑"
                }
            ]
        },
        {
            "id": "req_020",
            "query": "编码和解码处理",
            "ground_truth": [
                {
                    "file_path": "src/requests/utils.py",
                    "start_line": 400,
                    "end_line": 600,
                    "relevance_score": 1.0,
                    "explanation": "编码相关的工具函数"
                },
                {
                    "file_path": "src/requests/models.py",
                    "start_line": 400,
                    "end_line": 500,
                    "relevance_score": 0.8,
                    "explanation": "Response中的编码处理"
                }
            ]
        }
    ]
    
    return queries


def save_as_jsonl(queries: List[Dict[str, Any]], output_path: str):
    """保存为JSONL格式"""
    with open(output_path, 'w', encoding='utf-8') as f:
        for query in queries:
            f.write(json.dumps(query, ensure_ascii=False) + '\n')


def validate_jsonl(file_path: str) -> bool:
    """验证JSONL文件格式"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                if line.strip():
                    data = json.loads(line)
                    # 验证必需字段
                    required_fields = ['id', 'query', 'ground_truth']
                    for field in required_fields:
                        if field not in data:
                            print(f"第{line_num}行缺少必需字段: {field}")
                            return False
                    
                    # 验证ground_truth格式
                    for gt in data['ground_truth']:
                        gt_required = ['file_path', 'relevance_score', 'explanation']
                        for field in gt_required:
                            if field not in gt:
                                print(f"第{line_num}行ground_truth缺少字段: {field}")
                                return False
        
        print("✅ JSONL格式验证通过")
        return True
    except Exception as e:
        print(f"❌ JSONL格式验证失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 生成requests库评测数据集...")
    
    # 生成查询数据
    queries = create_requests_evaluation_dataset()
    
    # 保存为JSONL格式
    output_path = "requests_evaluation_dataset.jsonl"
    save_as_jsonl(queries, output_path)
    
    # 验证格式
    if validate_jsonl(output_path):
        print(f"✅ 生成完成！")
        print(f"📁 输出文件: {output_path}")
        print(f"📊 查询数量: {len(queries)}")
        
        # 统计信息
        total_ground_truth = sum(len(q['ground_truth']) for q in queries)
        avg_ground_truth = total_ground_truth / len(queries)
        
        print(f"📈 统计信息:")
        print(f"   总ground truth数量: {total_ground_truth}")
        print(f"   平均每个查询的ground truth: {avg_ground_truth:.1f}")
        
        # 显示前3条示例
        print(f"\n📝 前3条示例:")
        for i, query in enumerate(queries[:3], 1):
            print(f"   {i}. {query['id']}: {query['query']}")
            print(f"      期望文件: {query['ground_truth'][0]['file_path']}")
    else:
        print("❌ 数据集生成失败")


if __name__ == "__main__":
    main()
