{"summary": {"total_queries": 20, "successful_queries": 20, "failed_queries": 0, "success_rate": 1.0, "total_time": 0.0041599273681640625, "avg_time_per_query": 0.00020799636840820312}, "aggregated_metrics": {"precision@1": {"mean": 0.0, "min": 0.0, "max": 0.0, "count": 20}, "recall@1": {"mean": 0.0, "min": 0.0, "max": 0.0, "count": 20}, "f1@1": {"mean": 0.0, "min": 0.0, "max": 0.0, "count": 20}, "precision@3": {"mean": 0.0, "min": 0.0, "max": 0.0, "count": 20}, "recall@3": {"mean": 0.0, "min": 0.0, "max": 0.0, "count": 20}, "f1@3": {"mean": 0.0, "min": 0.0, "max": 0.0, "count": 20}, "precision@5": {"mean": 0.0, "min": 0.0, "max": 0.0, "count": 20}, "recall@5": {"mean": 0.0, "min": 0.0, "max": 0.0, "count": 20}, "f1@5": {"mean": 0.0, "min": 0.0, "max": 0.0, "count": 20}, "precision@10": {"mean": 0.0, "min": 0.0, "max": 0.0, "count": 20}, "recall@10": {"mean": 0.0, "min": 0.0, "max": 0.0, "count": 20}, "f1@10": {"mean": 0.0, "min": 0.0, "max": 0.0, "count": 20}, "hit_rate@5": {"mean": 0.0, "min": 0.0, "max": 0.0, "count": 20}}, "detailed_results": [{"query_id": "req_001", "query_text": "如何发送HTTP GET请求", "execution_time": 0.0003859996795654297, "metrics": {"precision@1": 0.0, "recall@1": 0.0, "f1@1": 0.0, "precision@3": 0.0, "recall@3": 0.0, "f1@3": 0.0, "precision@5": 0.0, "recall@5": 0.0, "f1@5": 0.0, "precision@10": 0.0, "recall@10": 0.0, "f1@10": 0.0, "hit_rate@5": 0.0}, "retrieved_files": [], "ground_truth_files": ["src/requests/api.py"], "success": true}, {"query_id": "req_002", "query_text": "如何处理HTTP响应", "execution_time": 0.000186920166015625, "metrics": {"precision@1": 0.0, "recall@1": 0.0, "f1@1": 0.0, "precision@3": 0.0, "recall@3": 0.0, "f1@3": 0.0, "precision@5": 0.0, "recall@5": 0.0, "f1@5": 0.0, "precision@10": 0.0, "recall@10": 0.0, "f1@10": 0.0, "hit_rate@5": 0.0}, "retrieved_files": [], "ground_truth_files": ["src/requests/models.py"], "success": true}, {"query_id": "req_003", "query_text": "Session会话管理的实现", "execution_time": 0.00018596649169921875, "metrics": {"precision@1": 0.0, "recall@1": 0.0, "f1@1": 0.0, "precision@3": 0.0, "recall@3": 0.0, "f1@3": 0.0, "precision@5": 0.0, "recall@5": 0.0, "f1@5": 0.0, "precision@10": 0.0, "recall@10": 0.0, "f1@10": 0.0, "hit_rate@5": 0.0}, "retrieved_files": [], "ground_truth_files": ["src/requests/sessions.py"], "success": true}, {"query_id": "req_004", "query_text": "HTTP适配器的作用", "execution_time": 0.00018596649169921875, "metrics": {"precision@1": 0.0, "recall@1": 0.0, "f1@1": 0.0, "precision@3": 0.0, "recall@3": 0.0, "f1@3": 0.0, "precision@5": 0.0, "recall@5": 0.0, "f1@5": 0.0, "precision@10": 0.0, "recall@10": 0.0, "f1@10": 0.0, "hit_rate@5": 0.0}, "retrieved_files": [], "ground_truth_files": ["src/requests/adapters.py"], "success": true}, {"query_id": "req_005", "query_text": "如何处理认证", "execution_time": 0.00018906593322753906, "metrics": {"precision@1": 0.0, "recall@1": 0.0, "f1@1": 0.0, "precision@3": 0.0, "recall@3": 0.0, "f1@3": 0.0, "precision@5": 0.0, "recall@5": 0.0, "f1@5": 0.0, "precision@10": 0.0, "recall@10": 0.0, "f1@10": 0.0, "hit_rate@5": 0.0}, "retrieved_files": [], "ground_truth_files": ["src/requests/auth.py"], "success": true}, {"query_id": "req_006", "query_text": "Cookie处理机制", "execution_time": 0.00018477439880371094, "metrics": {"precision@1": 0.0, "recall@1": 0.0, "f1@1": 0.0, "precision@3": 0.0, "recall@3": 0.0, "f1@3": 0.0, "precision@5": 0.0, "recall@5": 0.0, "f1@5": 0.0, "precision@10": 0.0, "recall@10": 0.0, "f1@10": 0.0, "hit_rate@5": 0.0}, "retrieved_files": [], "ground_truth_files": ["src/requests/cookies.py"], "success": true}, {"query_id": "req_007", "query_text": "异常处理和错误类型", "execution_time": 0.0001857280731201172, "metrics": {"precision@1": 0.0, "recall@1": 0.0, "f1@1": 0.0, "precision@3": 0.0, "recall@3": 0.0, "f1@3": 0.0, "precision@5": 0.0, "recall@5": 0.0, "f1@5": 0.0, "precision@10": 0.0, "recall@10": 0.0, "f1@10": 0.0, "hit_rate@5": 0.0}, "retrieved_files": [], "ground_truth_files": ["src/requests/exceptions.py"], "success": true}, {"query_id": "req_008", "query_text": "如何发送POST请求", "execution_time": 0.0001857280731201172, "metrics": {"precision@1": 0.0, "recall@1": 0.0, "f1@1": 0.0, "precision@3": 0.0, "recall@3": 0.0, "f1@3": 0.0, "precision@5": 0.0, "recall@5": 0.0, "f1@5": 0.0, "precision@10": 0.0, "recall@10": 0.0, "f1@10": 0.0, "hit_rate@5": 0.0}, "retrieved_files": [], "ground_truth_files": ["src/requests/api.py"], "success": true}, {"query_id": "req_009", "query_text": "工具函数和辅助方法", "execution_time": 0.00018477439880371094, "metrics": {"precision@1": 0.0, "recall@1": 0.0, "f1@1": 0.0, "precision@3": 0.0, "recall@3": 0.0, "f1@3": 0.0, "precision@5": 0.0, "recall@5": 0.0, "f1@5": 0.0, "precision@10": 0.0, "recall@10": 0.0, "f1@10": 0.0, "hit_rate@5": 0.0}, "retrieved_files": [], "ground_truth_files": ["src/requests/utils.py"], "success": true}, {"query_id": "req_010", "query_text": "HTTP状态码处理", "execution_time": 0.00019097328186035156, "metrics": {"precision@1": 0.0, "recall@1": 0.0, "f1@1": 0.0, "precision@3": 0.0, "recall@3": 0.0, "f1@3": 0.0, "precision@5": 0.0, "recall@5": 0.0, "f1@5": 0.0, "precision@10": 0.0, "recall@10": 0.0, "f1@10": 0.0, "hit_rate@5": 0.0}, "retrieved_files": [], "ground_truth_files": ["src/requests/status_codes.py"], "success": true}, {"query_id": "req_011", "query_text": "数据结构和容器类", "execution_time": 0.00018906593322753906, "metrics": {"precision@1": 0.0, "recall@1": 0.0, "f1@1": 0.0, "precision@3": 0.0, "recall@3": 0.0, "f1@3": 0.0, "precision@5": 0.0, "recall@5": 0.0, "f1@5": 0.0, "precision@10": 0.0, "recall@10": 0.0, "f1@10": 0.0, "hit_rate@5": 0.0}, "retrieved_files": [], "ground_truth_files": ["src/requests/structures.py"], "success": true}, {"query_id": "req_012", "query_text": "钩子函数机制", "execution_time": 0.0001888275146484375, "metrics": {"precision@1": 0.0, "recall@1": 0.0, "f1@1": 0.0, "precision@3": 0.0, "recall@3": 0.0, "f1@3": 0.0, "precision@5": 0.0, "recall@5": 0.0, "f1@5": 0.0, "precision@10": 0.0, "recall@10": 0.0, "f1@10": 0.0, "hit_rate@5": 0.0}, "retrieved_files": [], "ground_truth_files": ["src/requests/hooks.py"], "success": true}, {"query_id": "req_013", "query_text": "兼容性处理代码", "execution_time": 0.00018906593322753906, "metrics": {"precision@1": 0.0, "recall@1": 0.0, "f1@1": 0.0, "precision@3": 0.0, "recall@3": 0.0, "f1@3": 0.0, "precision@5": 0.0, "recall@5": 0.0, "f1@5": 0.0, "precision@10": 0.0, "recall@10": 0.0, "f1@10": 0.0, "hit_rate@5": 0.0}, "retrieved_files": [], "ground_truth_files": ["src/requests/compat.py"], "success": true}, {"query_id": "req_014", "query_text": "SSL证书处理", "execution_time": 0.00018906593322753906, "metrics": {"precision@1": 0.0, "recall@1": 0.0, "f1@1": 0.0, "precision@3": 0.0, "recall@3": 0.0, "f1@3": 0.0, "precision@5": 0.0, "recall@5": 0.0, "f1@5": 0.0, "precision@10": 0.0, "recall@10": 0.0, "f1@10": 0.0, "hit_rate@5": 0.0}, "retrieved_files": [], "ground_truth_files": ["src/requests/certs.py"], "success": true}, {"query_id": "req_015", "query_text": "如何设置请求头", "execution_time": 0.0002262592315673828, "metrics": {"precision@1": 0.0, "recall@1": 0.0, "f1@1": 0.0, "precision@3": 0.0, "recall@3": 0.0, "f1@3": 0.0, "precision@5": 0.0, "recall@5": 0.0, "f1@5": 0.0, "precision@10": 0.0, "recall@10": 0.0, "f1@10": 0.0, "hit_rate@5": 0.0}, "retrieved_files": [], "ground_truth_files": ["src/requests/models.py", "src/requests/sessions.py"], "success": true}, {"query_id": "req_016", "query_text": "超时设置和处理", "execution_time": 0.00019288063049316406, "metrics": {"precision@1": 0.0, "recall@1": 0.0, "f1@1": 0.0, "precision@3": 0.0, "recall@3": 0.0, "f1@3": 0.0, "precision@5": 0.0, "recall@5": 0.0, "f1@5": 0.0, "precision@10": 0.0, "recall@10": 0.0, "f1@10": 0.0, "hit_rate@5": 0.0}, "retrieved_files": [], "ground_truth_files": ["src/requests/adapters.py"], "success": true}, {"query_id": "req_017", "query_text": "代理服务器配置", "execution_time": 0.0001900196075439453, "metrics": {"precision@1": 0.0, "recall@1": 0.0, "f1@1": 0.0, "precision@3": 0.0, "recall@3": 0.0, "f1@3": 0.0, "precision@5": 0.0, "recall@5": 0.0, "f1@5": 0.0, "precision@10": 0.0, "recall@10": 0.0, "f1@10": 0.0, "hit_rate@5": 0.0}, "retrieved_files": [], "ground_truth_files": ["src/requests/adapters.py", "src/requests/sessions.py"], "success": true}, {"query_id": "req_018", "query_text": "文件上传功能", "execution_time": 0.00019502639770507812, "metrics": {"precision@1": 0.0, "recall@1": 0.0, "f1@1": 0.0, "precision@3": 0.0, "recall@3": 0.0, "f1@3": 0.0, "precision@5": 0.0, "recall@5": 0.0, "f1@5": 0.0, "precision@10": 0.0, "recall@10": 0.0, "f1@10": 0.0, "hit_rate@5": 0.0}, "retrieved_files": [], "ground_truth_files": ["src/requests/models.py"], "success": true}, {"query_id": "req_019", "query_text": "重定向处理机制", "execution_time": 0.0002002716064453125, "metrics": {"precision@1": 0.0, "recall@1": 0.0, "f1@1": 0.0, "precision@3": 0.0, "recall@3": 0.0, "f1@3": 0.0, "precision@5": 0.0, "recall@5": 0.0, "f1@5": 0.0, "precision@10": 0.0, "recall@10": 0.0, "f1@10": 0.0, "hit_rate@5": 0.0}, "retrieved_files": [], "ground_truth_files": ["src/requests/sessions.py"], "success": true}, {"query_id": "req_020", "query_text": "编码和解码处理", "execution_time": 0.00018906593322753906, "metrics": {"precision@1": 0.0, "recall@1": 0.0, "f1@1": 0.0, "precision@3": 0.0, "recall@3": 0.0, "f1@3": 0.0, "precision@5": 0.0, "recall@5": 0.0, "f1@5": 0.0, "precision@10": 0.0, "recall@10": 0.0, "f1@10": 0.0, "hit_rate@5": 0.0}, "retrieved_files": [], "ground_truth_files": ["src/requests/utils.py", "src/requests/models.py"], "success": true}], "timestamp": "2025-06-25 14:10:27"}