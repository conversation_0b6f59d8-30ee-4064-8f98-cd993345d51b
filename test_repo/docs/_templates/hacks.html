<!-- Alabaster (krTheme++) Hacks -->

<!-- CSS Adjustments (I'm very picky.) -->
<style type="text/css">

  /* <PERSON><PERSON> requires precise alignment. */
  img.logo {margin-left: -20px!important;}

  /* "Quick Search" should be capitalized. */
  div#searchbox h3 {text-transform: capitalize;}

  /* Make the document a little wider, less code is cut-off. */
  div.document {width: 1008px;}

  /* Much-improved spacing around code blocks. */
  div.highlight pre {padding: 11px 14px;}

  /* Remain Responsive! */
  @media screen and (max-width: 1008px) {
    div.sphinxsidebar {display: none;}
    div.document {width: 100%!important;}

    /* Have code blocks escape the document right-margin. */
    div.highlight pre {margin-right: -30px;}
  }
</style>

<!-- Native CPC by BuySellAds -->

<script type="text/javascript" src="//m.servedby-buysellads.com/monetization.js"></script>

<div id="native-ribbon">
</div>

<script>
_bsa.init('custom', 'CK7D62JU', 'placement:pythonrequestsorg',
    {
      target: '#native-ribbon',
      template: `
<div class="native-sponsor">Sponsored by ##company## — Learn More</div>
  <a href="##link##" class="native-flex">
    <style>
    #native-ribbon #_custom_ {
      background: linear-gradient(-30deg, ##backgroundColor##E5, ##backgroundColor##E5 45%, ##backgroundColor## 45%) #fff;
    }

    .native-details,
    .native-sponsor,
    .native-bsa {
      color: ##textColor## !important;
    }

    .native-details:hover {
      color: ##textColorHover## !important;
    }

    .native-cta {
      color: ##ctaTextColor##;
      background-color: ##ctaBackgroundColor##;
    }

    .native-cta:hover {
      color: ##ctaTextColorHover##;
      background-color: ##ctaBackgroundColorHover##;
    }
    </style>
    <div class="native-main">
      <img class="native-img" src="##logo##">
      <div class="native-details">
        <span class="native-company">##title##</span>
        <span class="native-desc">##description##</span>
      </div>
    </div>
    <span class="native-cta">##callToAction##</span>
  </a>
</div>
`
    }
  );
</script>
