name: Publish to Py<PERSON>

on:
  push:
    tags:
      - "v*"

permissions:
  contents: read

jobs:
  build:
    name: "Build dists"
    runs-on: "ubuntu-latest"
    environment:
      name: "publish"
    outputs:
      hashes: ${{ steps.hash.outputs.hashes }}

    steps:
      - name: Harden the runner (Audit all outbound calls)
        uses: step-security/harden-runner@0634a2670c59f64b4a01f0f96f84700a4088b9f0 # v2.12.0
        with:
          egress-policy: audit

      - name: "Checkout repository"
        uses: "actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683"

      - name: "Setup Python"
        uses: "actions/setup-python@a26af69be951a213d495a4c3e4e4022e16d87065"
        with:
          python-version: "3.x"

      - name: "Install dependencies"
        run: python -m pip install build==0.8.0

      - name: "Build dists"
        run: |
          SOURCE_DATE_EPOCH=$(git log -1 --pretty=%ct) \
          python -m build

      - name: "Generate hashes"
        id: hash
        run: |
          cd dist && echo "::set-output name=hashes::$(sha256sum * | base64 -w0)"

      - name: "Upload dists"
        uses: "actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02"
        with:
          name: "dist"
          path: "dist/"
          if-no-files-found: error
          retention-days: 5

  provenance:
    needs: [build]
    permissions:
      actions: read
      contents: write
      id-token: write # Needed to access the workflow's OIDC identity.
    uses: "slsa-framework/slsa-github-generator/.github/workflows/generator_generic_slsa3.yml@v2.1.0"
    with:
      base64-subjects: "${{ needs.build.outputs.hashes }}"
      upload-assets: true
      compile-generator: true # Workaround for https://github.com/slsa-framework/slsa-github-generator/issues/1163

  publish:
    name: "Publish"
    if: startsWith(github.ref, 'refs/tags/')
    needs: ["build", "provenance"]
    permissions:
      contents: write
      id-token: write
    runs-on: "ubuntu-latest"

    steps:
    - name: Harden the runner (Audit all outbound calls)
      uses: step-security/harden-runner@0634a2670c59f64b4a01f0f96f84700a4088b9f0 # v2.12.0
      with:
        egress-policy: audit

    - name: "Download dists"
      uses: "actions/download-artifact@d3f86a106a0bac45b974a628896c90dbdf5c8093"
      with:
        name: "dist"
        path: "dist/"

    - name: "Publish dists to PyPI"
      uses: "pypa/gh-action-pypi-publish@76f52bc884231f62b9a034ebfe128415bbaabdfc"
