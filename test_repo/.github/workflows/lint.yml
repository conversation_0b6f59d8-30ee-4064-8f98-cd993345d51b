name: <PERSON>t code

on: [push, pull_request]

permissions:
  contents: read

jobs:
  lint:
    runs-on: ubuntu-24.04
    timeout-minutes: 10

    steps:
    - uses: actions/checkout@d632683dd7b4114ad314bca15554477dd762a938 # v4.2.0
    - name: Set up Python
      uses: actions/setup-python@a26af69be951a213d495a4c3e4e4022e16d87065 # v5.6.0
      with:
        python-version: "3.x"
    - name: Run pre-commit
      uses: pre-commit/action@646c83fcd040023954eafda54b4db0192ce70507 # v3.0.0
