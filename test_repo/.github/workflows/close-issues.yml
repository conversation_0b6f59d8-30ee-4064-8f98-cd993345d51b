name: 'Autoclose Issues'

on:
  issues:
    types:
      - labeled

permissions:
  issues: write

jobs:
  close_qa:
    if: github.event.label.name == 'actions/autoclose-qa'
    runs-on: ubuntu-latest
    steps:
      - env:
          ISSUE_URL: ${{ github.event.issue.html_url }}
          GH_TOKEN: ${{ github.token }}
        run: |
          gh issue close $ISSUE_URL \
            --comment "As described in the template, we won't be able to answer questions on this issue tracker. Please use [Stack Overflow](https://stackoverflow.com/)" \
            --reason completed
          gh issue lock $ISSUE_URL --reason off_topic
  close_feature_request:
    if: github.event.label.name == 'actions/autoclose-feat'
    runs-on: ubuntu-latest
    steps:
      - env:
          ISSUE_URL: ${{ github.event.issue.html_url }}
          GH_TOKEN: ${{ github.token }}
        run: |
          gh issue close $ISSUE_URL \
            --comment "As described in the template, Requests is not accepting feature requests" \
            --reason "not planned"
          gh issue lock $ISSUE_URL --reason off_topic
