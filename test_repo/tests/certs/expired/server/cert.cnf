[req]
req_extensions = v3_req
distinguished_name = req_distinguished_name
prompt=no

[req_distinguished_name]
C = US
ST = DE
O = Python Software Foundation
OU = python-requests
CN = localhost

[v3_req]
# Extensions to add to a certificate request
basicConstraints = CA:FALSE
keyUsage = digitalSignature, keyEncipherment
extendedKeyUsage = serverAuth
subjectAltName = @alt_names

[alt_names]
DNS.1 = *.localhost
DNS.1 = localhost
IP.1 = 127.0.0.1
IP.2 = ::1
