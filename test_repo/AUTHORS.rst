Requests was lovingly created by <PERSON>.

Keepers of the Crystals
```````````````````````

- <PERSON> `@nateprewitt <https://github.com/nateprewitt>`_.
- <PERSON> `@sethmlarson <https://github.com/sethmlars<PERSON>>`_.

Previous Keepers of Crystals
````````````````````````````
- <PERSON> <<EMAIL>> `@kennethreitz <https://github.com/kennethreitz>`_, reluctant Keeper of the Master Crystal.
- <PERSON> <<EMAIL>> `@lukasa <https://github.com/lukasa>`_
- <PERSON> <<EMAIL>> `@sigmavirus24 <https://github.com/sigmavirus24>`_.


Patches and Suggestions
```````````````````````

- Various Pocoo Members
- <PERSON>
- <PERSON><PERSON><PERSON>
- Dj <PERSON><PERSON><PERSON>
- <PERSON>
- <PERSON>
- <PERSON>
- <PERSON>
- 村山めがね (<PERSON><PERSON>)
- <PERSON>
- <PERSON>
- <PERSON><PERSON>
- <PERSON>'
- <PERSON>
- <PERSON>livares <<EMAIL>>
- Alberto Paro
- Jérémy Bethmont
- 潘旭 (Xu Pan)
- Tamás Gulácsi
- Rubén Abad
- Peter Manser
- Jeremy Selier
- Jens Diemer
- Alex (`@alopatin <https://github.com/alopatin>`_)
- Tom Hogans <<EMAIL>>
- Armin Ronacher
- Shrikant Sharat Kandula
- Mikko Ohtamaa
- Den Shabalin
- Daniel Miller <<EMAIL>>
- Alejandro Giacometti
- Rick Mak
- Johan Bergström
- Josselin Jacquard
- Travis N. Vaught
- Fredrik Möllerstrand
- Daniel Hengeveld
- Dan Head
- Bruno Renié
- David Fischer
- Joseph McCullough
- Juergen Brendel
- Juan Riaza
- Ryan Kelly
- Rolando Espinoza La fuente
- Robert Gieseke
- Idan Gazit
- Ed Summers
- Chris Van Horne
- Christopher Davis
- Ori Livneh
- Jason Emerick
- Bryan Helmig
- Jonas Obrist
- Lucian Ursu
- Tom Moertel
- Frank Kumro Jr
- Chase Sterling
- Marty Alchin
- takluyver
- Ben Toews (`@mastahyeti <https://github.com/mastahyeti>`_)
- David Kemp
- Brendon Crawford
- Denis (`@Telofy <https://github.com/Telofy>`_)
- Matt Giuca
- Adam Tauber
- Honza Javorek
- Brendan Maguire <<EMAIL>>
- Chris Dary
- Danver Braganza <<EMAIL>>
- Max Countryman
- Nick Chadwick
- Jonathan Drosdeck
- Jiri Machalek
- Steve Pulec
- Michael Kelly
- Michael Newman <<EMAIL>>
- Jonty Wareing <<EMAIL>>
- Shivaram Lingamneni
- Miguel Turner
- Rohan Jain (`@crodjer <https://github.com/crodjer>`_)
- Justin Barber <<EMAIL>>
- Roman Haritonov (`@reclosedev <https://github.com/reclosedev>`_)
- Josh Imhoff <<EMAIL>>
- Arup Malakar <<EMAIL>>
- Danilo Bargen (`@dbrgn <https://github.com/dbrgn>`_)
- Torsten Landschoff
- Michael Holler (`@apotheos <https://github.com/apotheos>`_)
- Timnit Gebru
- Sarah Gonzalez
- Victoria Mo
- Leila Muhtasib
- Matthias Rahlf <<EMAIL>>
- Jakub Roztocil <<EMAIL>>
- Rhys Elsmore
- André Graf (`@dergraf <https://github.com/dergraf>`_)
- Stephen Zhuang (`@everbird <https://github.com/everbird>`_)
- Martijn Pieters
- Jonatan Heyman
- David Bonner <<EMAIL>> (`@rascalking <https://github.com/rascalking>`_)
- Vinod Chandru
- Johnny Goodnow <<EMAIL>>
- Denis Ryzhkov <<EMAIL>>
- Wilfred Hughes <<EMAIL>>
- Dmitry Medvinsky <<EMAIL>>
- Bryce Boe <<EMAIL>> (`@bboe <https://github.com/bboe>`_)
- Colin Dunklau <<EMAIL>> (`@cdunklau <https://github.com/cdunklau>`_)
- Bob Carroll <<EMAIL>> (`@rcarz <https://github.com/rcarz>`_)
- Hugo Osvaldo Barrera <<EMAIL>> (`@hobarrera <https://github.com/hobarrera>`_)
- Łukasz Langa <<EMAIL>>
- Dave Shawley <<EMAIL>>
- James Clarke (`@jam <https://github.com/jam>`_)
- Kevin Burke <<EMAIL>>
- Flavio Curella
- David Pursehouse <<EMAIL>> (`@dpursehouse <https://github.com/dpursehouse>`_)
- Jon Parise (`@jparise <https://github.com/jparise>`_)
- Alexander Karpinsky (`@homm86 <https://twitter.com/homm86>`_)
- Marc Schlaich (`@schlamar <https://github.com/schlamar>`_)
- Park Ilsu <<EMAIL>> (`@daftshady <https://github.com/daftshady>`_)
- Matt Spitz (`@mattspitz <https://github.com/mattspitz>`_)
- Vikram Oberoi (`@voberoi <https://github.com/voberoi>`_)
- Can Ibanoglu <<EMAIL>> (`@canibanoglu <https://github.com/canibanoglu>`_)
- Thomas Weißschuh <<EMAIL>> (`@t-8ch <https://github.com/t-8ch>`_)
- Jayson Vantuyl <<EMAIL>>
- Pengfei.X <<EMAIL>>
- Kamil Madac <<EMAIL>>
- Michael Becker <<EMAIL>> (`@beckerfuffle <https://twitter.com/beckerfuffle>`_)
- Erik Wickstrom <<EMAIL>> (`@erikwickstrom <https://github.com/erikwickstrom>`_)
- Константин Подшумок (`@podshumok <https://github.com/podshumok>`_)
- Ben Bass (`@codedstructure <https://github.com/codedstructure>`_)
- Jonathan Wong <<EMAIL>> (`@ContinuousFunction <https://github.com/ContinuousFunction>`_)
- Martin Jul (`@mjul <https://github.com/mjul>`_)
- Joe Alcorn (`@buttscicles <https://github.com/buttscicles>`_)
- Syed Suhail Ahmed <<EMAIL>> (`@syedsuhail <https://github.com/syedsuhail>`_)
- Scott Sadler (`@ssadler <https://github.com/ssadler>`_)
- Arthur Darcet (`@arthurdarcet <https://github.com/arthurdarcet>`_)
- Ulrich Petri (`@ulope <https://github.com/ulope>`_)
- Muhammad Yasoob Ullah Khalid <<EMAIL>> (`@yasoob <https://github.com/yasoob>`_)
- Paul van der Linden (`@pvanderlinden <https://github.com/pvanderlinden>`_)
- Colin Dickson (`@colindickson <https://github.com/colindickson>`_)
- Smiley Barry (`@smiley <https://github.com/smiley>`_)
- Shagun Sodhani (`@shagunsodhani <https://github.com/shagunsodhani>`_)
- Robin Linderborg (`@vienno <https://github.com/vienno>`_)
- Brian Samek (`@bsamek <https://github.com/bsamek>`_)
- Dmitry Dygalo (`@Stranger6667 <https://github.com/Stranger6667>`_)
- piotrjurkiewicz
- Jesse Shapiro <<EMAIL>> (`@haikuginger <https://github.com/haikuginger>`_)
- Nate Prewitt <<EMAIL>> (`@nateprewitt <https://github.com/nateprewitt>`_)
- Maik Himstedt
- Michael Hunsinger
- Brian Bamsch <<EMAIL>> (`@bbamsch <https://github.com/bbamsch>`_)
- Om Prakash Kumar <<EMAIL>> (`@iamprakashom <https://github.com/iamprakashom>`_)
- Philipp Konrad <<EMAIL>> (`@gardiac2002 <https://github.com/gardiac2002>`_)
- Hussain Tamboli <<EMAIL>> (`@hussaintamboli <https://github.com/hussaintamboli>`_)
- Casey Davidson (`@davidsoncasey <https://github.com/davidsoncasey>`_)
- Andrii Soldatenko (`@a_soldatenko <https://github.com/andriisoldatenko>`_)
- Moinuddin Quadri <<EMAIL>> (`@moin18 <https://github.com/moin18>`_)
- Matt Kohl (`@mattkohl <https://github.com/mattkohl>`_)
- Jonathan Vanasco (`@jvanasco <https://github.com/jvanasco>`_)
- David Fontenot (`@davidfontenot <https://github.com/davidfontenot>`_)
- Shmuel Amar (`@shmuelamar <https://github.com/shmuelamar>`_)
- Gary Wu (`@garywu <https://github.com/garywu>`_)
- Ryan Pineo (`@ryanpineo <https://github.com/ryanpineo>`_)
- Ed Morley (`@edmorley <https://github.com/edmorley>`_)
- Matt Liu <<EMAIL>> (`@mlcrazy <https://github.com/mlcrazy>`_)
- Taylor Hoff <<EMAIL>> (`@PrimordialHelios <https://github.com/PrimordialHelios>`_)
- Arthur Vigil (`@ahvigil <https://github.com/ahvigil>`_)
- Nehal J Wani (`@nehaljwani <https://github.com/nehaljwani>`_)
- Demetrios Bairaktaris (`@DemetriosBairaktaris <https://github.com/demetriosbairaktaris>`_)
- Darren Dormer (`@ddormer <https://github.com/ddormer>`_)
- Rajiv Mayani (`@mayani <https://github.com/mayani>`_)
- Antti Kaihola (`@akaihola <https://github.com/akaihola>`_)
- "Dull Bananas" <<EMAIL>> (`@dullbananas <https://github.com/dullbananas>`_)
- Alessio Izzo (`@aless10 <https://github.com/aless10>`_)
- Sylvain Marié (`@smarie <https://github.com/smarie>`_)
- Hod Bin Noon (`@hodbn <https://github.com/hodbn>`_)
- Mike Fiedler (`@miketheman <https://github.com/miketheman>`_)
