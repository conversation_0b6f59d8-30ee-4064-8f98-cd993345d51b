"""
FastMCP Server for Codebase QA
使用FastMCP实现的代码库检索服务器，更简单易用
"""
import os
import json
from typing import List, Dict, Any, Optional
from contextlib import asynccontextmanager
from collections.abc import AsyncIterator
from dataclasses import dataclass

try:
    from mcp.server.fastmcp import FastMCP, Context
    from mcp.server.fastmcp.prompts import base
except ImportError:
    print("请安装MCP Python SDK: pip install mcp")
    raise

# 导入我们的pipeline
from pipeline_simple import Pipeline as SimplePipeline
from pipeline_pro import Pipeline as ProPipeline
from pipeline_balance import Pipeline as BalancePipeline
from pipeline_best import Pipeline as BestPipeline
from utils import Config, setup_logger

logger = setup_logger(__name__)


@dataclass
class CodebaseContext:
    """代码库上下文"""
    config: Config
    pipeline: Any
    pipeline_type: str


@asynccontextmanager
async def app_lifespan(server: FastMCP) -> AsyncIterator[CodebaseContext]:
    """管理应用生命周期"""
    logger.info("初始化Codebase MCP服务器")
    
    # 初始化配置
    config = Config()
    try:
        config.validate()
    except ValueError as e:
        logger.error(f"配置验证失败: {e}")
        raise
    
    # 选择pipeline类型
    pipeline_type = os.environ.get("PIPELINE_TYPE", "pro").lower()
    pipeline_classes = {
        "simple": SimplePipeline,
        "pro": ProPipeline,
        "balance": BalancePipeline,
        "best": BestPipeline
    }
    
    if pipeline_type not in pipeline_classes:
        logger.warning(f"未知的pipeline类型: {pipeline_type}，使用默认的pro类型")
        pipeline_type = "pro"
    
    PipelineClass = pipeline_classes[pipeline_type]
    logger.info(f"使用 {pipeline_type} pipeline")
    
    # 获取索引路径
    repo_name = os.path.basename(config.repo_path)
    index_paths = config.get_index_paths(repo_name)
    
    # 初始化pipeline
    try:
        if pipeline_type == "simple":
            pipeline = PipelineClass(
                bm25_persist_path=index_paths["bm25"],
                emb_persist_path=index_paths["embedding"]
            )
        else:
            pipeline = PipelineClass(
                bm25_persist_path=index_paths["bm25"],
                emb_persist_path=index_paths["embedding"],
                graph_persist_path=index_paths["graph"]
            )
        
        logger.info("Pipeline初始化成功")
        
        # 创建上下文
        context = CodebaseContext(
            config=config,
            pipeline=pipeline,
            pipeline_type=pipeline_type
        )
        
        yield context
        
    except Exception as e:
        logger.error(f"Pipeline初始化失败: {e}")
        raise
    finally:
        logger.info("清理Codebase资源")


# 创建FastMCP服务器
mcp = FastMCP("Codebase QA", lifespan=app_lifespan)


@mcp.resource("codebase://config")
def get_config() -> str:
    """获取代码库配置信息"""
    ctx = mcp.get_context()
    codebase_ctx = ctx.request_context.lifespan_context
    
    config_info = {
        "repo_path": codebase_ctx.config.repo_path,
        "pipeline_type": codebase_ctx.pipeline_type,
        "retrieval_config": codebase_ctx.config.retrieval.model_dump(),
        "chunk_config": codebase_ctx.config.chunk.model_dump()
    }
    return json.dumps(config_info, indent=2, ensure_ascii=False)


@mcp.resource("codebase://stats")
def get_stats() -> str:
    """获取代码库统计信息"""
    ctx = mcp.get_context()
    codebase_ctx = ctx.request_context.lifespan_context
    
    stats_info = {
        "pipeline_type": codebase_ctx.pipeline_type,
        "repo_name": os.path.basename(codebase_ctx.config.repo_path),
        "available_retrievers": []
    }
    
    # 检查可用的检索器
    pipeline = codebase_ctx.pipeline
    if hasattr(pipeline, 'bm25_retriever') and pipeline.bm25_retriever:
        stats_info["available_retrievers"].append("bm25")
    if hasattr(pipeline, 'emb_retriever') and pipeline.emb_retriever:
        stats_info["available_retrievers"].append("embedding")
    if hasattr(pipeline, 'graph_retriever') and pipeline.graph_retriever:
        stats_info["available_retrievers"].append("graph")
        
    return json.dumps(stats_info, indent=2, ensure_ascii=False)


@mcp.tool()
def search_codebase(query: str, top_k: int = 5) -> str:
    """
    在代码库中搜索相关的代码片段
    
    Args:
        query: 搜索查询，使用自然语言描述你要找的代码功能
        top_k: 返回的结果数量，默认为5
    
    Returns:
        格式化的搜索结果
    """
    ctx = mcp.get_context()
    codebase_ctx = ctx.request_context.lifespan_context
    
    try:
        # 执行搜索
        results = codebase_ctx.pipeline.run(query, top_k=top_k)
        
        # 格式化结果
        if not results:
            return f"未找到与查询 '{query}' 相关的代码片段。"
        
        formatted_results = []
        formatted_results.append(f"找到 {len(results)} 个相关的代码片段：\n")
        
        for i, result in enumerate(results, 1):
            file_path = result.get("file_path", "未知文件")
            text = result.get("text", "")
            score = result.get("score", 0)
            source = result.get("source", "unknown")
            
            formatted_results.append(f"## 结果 {i} (来源: {source}, 相关度: {score:.3f})")
            formatted_results.append(f"**文件**: {file_path}")
            formatted_results.append(f"**内容**:")
            formatted_results.append(f"```")
            formatted_results.append(text)
            formatted_results.append(f"```\n")
        
        return "\n".join(formatted_results)
        
    except Exception as e:
        logger.error(f"搜索失败: {e}")
        return f"搜索失败: {str(e)}"


@mcp.tool()
def get_file_content(file_path: str) -> str:
    """
    获取指定文件的完整内容
    
    Args:
        file_path: 相对于代码库根目录的文件路径
    
    Returns:
        文件内容
    """
    ctx = mcp.get_context()
    codebase_ctx = ctx.request_context.lifespan_context
    
    try:
        # 构建完整路径
        full_path = os.path.join(codebase_ctx.config.repo_path, file_path)
        
        # 安全检查：确保文件在代码库目录内
        if not os.path.abspath(full_path).startswith(os.path.abspath(codebase_ctx.config.repo_path)):
            return "错误：文件路径超出代码库范围"
        
        if not os.path.exists(full_path):
            return f"错误：文件 {file_path} 不存在"
        
        # 读取文件内容
        with open(full_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        return f"文件: {file_path}\n\n```\n{content}\n```"
        
    except Exception as e:
        logger.error(f"读取文件失败: {e}")
        return f"读取文件失败: {str(e)}"


@mcp.tool()
def analyze_code_structure(file_path: str) -> str:
    """
    分析代码文件的结构（类、函数等）
    
    Args:
        file_path: 相对于代码库根目录的文件路径
    
    Returns:
        代码结构分析结果
    """
    ctx = mcp.get_context()
    codebase_ctx = ctx.request_context.lifespan_context
    
    try:
        # 构建完整路径
        full_path = os.path.join(codebase_ctx.config.repo_path, file_path)
        
        # 安全检查
        if not os.path.abspath(full_path).startswith(os.path.abspath(codebase_ctx.config.repo_path)):
            return "错误：文件路径超出代码库范围"
        
        if not os.path.exists(full_path):
            return f"错误：文件 {file_path} 不存在"
        
        # 简单的代码结构分析（可以扩展为使用AST）
        with open(full_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        lines = content.split('\n')
        structure = {
            "classes": [],
            "functions": [],
            "imports": [],
            "total_lines": len(lines)
        }
        
        for i, line in enumerate(lines, 1):
            stripped = line.strip()
            if stripped.startswith('class '):
                class_name = stripped.split('(')[0].replace('class ', '').strip(':')
                structure["classes"].append(f"第{i}行: {class_name}")
            elif stripped.startswith('def '):
                func_name = stripped.split('(')[0].replace('def ', '')
                structure["functions"].append(f"第{i}行: {func_name}")
            elif stripped.startswith(('import ', 'from ')):
                structure["imports"].append(f"第{i}行: {stripped}")
        
        result = [f"文件: {file_path}"]
        result.append(f"总行数: {structure['total_lines']}")
        
        if structure["imports"]:
            result.append(f"\n导入语句 ({len(structure['imports'])} 个):")
            for imp in structure["imports"][:10]:  # 限制显示数量
                result.append(f"  {imp}")
        
        if structure["classes"]:
            result.append(f"\n类定义 ({len(structure['classes'])} 个):")
            for cls in structure["classes"]:
                result.append(f"  {cls}")
        
        if structure["functions"]:
            result.append(f"\n函数定义 ({len(structure['functions'])} 个):")
            for func in structure["functions"][:20]:  # 限制显示数量
                result.append(f"  {func}")
        
        return "\n".join(result)
        
    except Exception as e:
        logger.error(f"分析代码结构失败: {e}")
        return f"分析代码结构失败: {str(e)}"


@mcp.prompt()
def code_review_prompt(file_path: str, focus_area: str = "general") -> List[base.Message]:
    """
    生成代码审查提示
    
    Args:
        file_path: 要审查的文件路径
        focus_area: 关注领域 (general, performance, security, style)
    
    Returns:
        代码审查提示消息
    """
    focus_descriptions = {
        "general": "进行全面的代码审查",
        "performance": "重点关注性能优化",
        "security": "重点关注安全问题",
        "style": "重点关注代码风格和可读性"
    }
    
    description = focus_descriptions.get(focus_area, "进行全面的代码审查")
    
    return [
        base.UserMessage(f"请对文件 {file_path} {description}。"),
        base.UserMessage("请分析以下方面："),
        base.UserMessage("1. 代码质量和可读性"),
        base.UserMessage("2. 潜在的bug或问题"),
        base.UserMessage("3. 改进建议"),
        base.UserMessage("4. 最佳实践的遵循情况"),
        base.AssistantMessage("我将仔细审查这个文件并提供详细的分析和建议。请提供文件内容。")
    ]


@mcp.prompt()
def search_help_prompt() -> str:
    """搜索帮助提示"""
    return """# Codebase搜索帮助

## 搜索技巧
1. 使用自然语言描述你要找的功能
2. 可以搜索特定的类名、函数名或概念
3. 支持中英文搜索

## 示例查询
- "如何处理HTTP请求"
- "数据库连接相关的代码"
- "用户认证功能"
- "配置文件读取"
- "日志记录实现"

## 可用工具
- `search_codebase`: 搜索相关代码片段
- `get_file_content`: 获取完整文件内容
- `analyze_code_structure`: 分析代码结构

使用这些工具可以帮助你快速理解和导航代码库！"""


if __name__ == "__main__":
    # 运行服务器
    mcp.run()
