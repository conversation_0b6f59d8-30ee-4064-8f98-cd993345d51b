(program . (_) @child.first @definition.module) @root

(class_declaration
  (type_identifier) @identifier
  (class_heritage
    (extends_clause
      (identifier) @reference.type
    )?
    (implements_clause
      (type_identifier) @reference.type
    )?
  )?
  (class_body
    ("{") @child.first
  )
) @root @definition.class

(interface_declaration
  (type_identifier) @identifier
  (extends_type_clause
    (type_identifier) @reference.type
  )?
  (object_type
    ("{") @child.first
  )
) @root @definition.class

(type_alias_declaration
  (type_identifier) @identifier
  (type_annotation
    (_) @reference.type
  )
) @root @definition.assignment

(enum_declaration
  (identifier) @identifier
  (enum_body
    ("{") @child.first
  )
) @root @definition.class

(function_declaration
  (identifier) @identifier
  (formal_parameters
    ("(")
    (
      [
        (required_parameter
          (identifier) @parameter.identifier
          (type_annotation
            (_) @parameter.type
          )?
        )
        (optional_parameter
          (identifier) @parameter.identifier
          (type_annotation
            (_) @parameter.type
          )?
        )
      ]
      (",")?
    )*
    (")")
  )
  (type_annotation
    (_) @reference.type
  )?
  (statement_block
    ("{") @child.first
  )
) @root @definition.function

(method_definition
  (property_identifier) @identifier
  (formal_parameters
    ("(")
    (
      [
        (required_parameter
          (identifier) @parameter.identifier
          (type_annotation
            (_) @parameter.type
          )?
        )
        (optional_parameter
          (identifier) @parameter.identifier
          (type_annotation
            (_) @parameter.type
          )?
        )
      ]
      (",")?
    )*
    (")")
  )
  (type_annotation
    (_) @reference.type
  )?
  (statement_block
    ("{") @child.first
  )
) @root @definition.function

(method_definition
  (property_identifier) @identifier
  (formal_parameters)
  (statement_block
    ("{") @child.first
  )
) @root @definition.constructor

(arrow_function
  (formal_parameters
    ("(")
    (
      [
        (required_parameter
          (identifier) @parameter.identifier
          (type_annotation
            (_) @parameter.type
          )?
        )
      ]
      (",")?
    )*
    (")")
  )
  (type_annotation
    (_) @reference.type
  )?
  [
    (statement_block
      ("{") @child.first
    )
    (_) @child.first
  ]
) @root @definition.function

(variable_declaration
  (variable_declarator
    (identifier) @identifier
    (type_annotation
      (_) @reference.type
    )?
    (_) @child.first
  )
) @root @definition.assignment

(public_field_definition
  (property_identifier) @identifier
  (type_annotation
    (_) @reference.type
  )?
  (_) @child.first
) @root @definition.assignment

(import_statement
  (import_clause
    [
      (identifier) @identifier
      (named_imports
        (import_specifier
          (identifier) @identifier
        )
      )
      (namespace_import
        (identifier) @identifier
      )
    ]
  )
  (string) @reference.module
) @root @definition.import

(export_statement
  [
    (function_declaration) @check_child
    (class_declaration) @check_child
    (interface_declaration) @check_child
    (type_alias_declaration) @check_child
    (variable_declaration) @check_child
  ]
) @root

(call_expression
  [
    (identifier) @reference.identifier
    (member_expression) @reference.identifier
  ]
  (arguments
    ("(")
    (
      [
        (identifier) @reference.identifier
        (_)
      ]
      (",")?
    )*
    (")")
  )
) @root @definition.call

(comment) @root @definition.comment

(if_statement
  (statement_block
    ("{") @child.first
  )
) @root @definition.compound

(for_statement
  (statement_block
    ("{") @child.first
  )
) @root @definition.compound

(while_statement
  (statement_block
    ("{") @child.first
  )
) @root @definition.compound

(try_statement
  (statement_block
    ("{") @child.first
  )
) @root @definition.compound

(catch_clause
  (statement_block
    ("{") @child.first
  )
) @root @definition.dependent_clause

(finally_clause
  (statement_block
    ("{") @child.first
  )
) @root @definition.dependent_clause

(_
  (statement_block
    . ("{") @child.first
  )
) @root @definition.statement