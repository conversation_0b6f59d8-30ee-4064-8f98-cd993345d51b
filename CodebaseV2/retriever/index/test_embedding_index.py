# SPDX-License-Identifier: Apache-2.0
# SPDX-FileCopyrightText: Copyright The LanceDB Authors
import base64
import os
from typing import ClassVar, List, Union, Any, overload

from pathlib import Path
from urllib.parse import urlparse
from io import BytesIO

import numpy as np
from ollama import embed
from overrides import override
import pyarrow as pa

from lancedb.util import attempt_import_or_raise
from lancedb.embeddings.base import EmbeddingFunction
from lancedb.embeddings.registry import register
from lancedb.embeddings.utils import api_key_not_found_help, IMAGES, TEXT
from tqdm import cli


def sanitize_text_input(inputs: TEXT) -> List[str]:
    """
    Sanitize the input to the embedding function.
    """
    if isinstance(inputs, str):
        inputs = [inputs]
    else:
        raise ValueError(
            f"Input type {type(inputs)} not allowed with text model."
        )

    if not all(isinstance(x, str) for x in inputs):
        raise ValueError("Each input should be str.")

    return inputs


@register("dashscope")
class DashscopeEmbeddingFunction(EmbeddingFunction):
    """
    An embedding function that uses the VoyageAI API


    Parameters
    ----------
    name: str
        The name of the model to use. List of acceptable models:

            * text-embedding-v4
            * text-embedding-v3
            * text-embedding-v2
            * text-embedding-v1
            * text-embedding-async-v2
            * text-embedding-async-v1


    Examples
    --------
    import lancedb
    from lancedb.pydantic import LanceModel, Vector
    from lancedb.embeddings import EmbeddingFunctionRegistry

    dashscopeai = EmbeddingFunctionRegistry
        .get_instance()
        .get("dashscope")
        .create(name="text-embedding-v4")

    class TextModel(LanceModel):
        text: str = dashscopeai.SourceField()
        vector: Vector(dashscopeai.ndims()) =  dashscopeai.VectorField()

    data = [ { "text": "hello world" },
            { "text": "goodbye world" }]

    db = lancedb.connect("~/.lancedb")
    tbl = db.create_table("test", schema=TextModel, mode="overwrite")

    tbl.add(data)

    """

    name: str
    client: ClassVar = None
    text_embedding_models: list = [
        "text-embedding-v4",
        "text-embedding-v3",
        "text-embedding-v2",
        "text-embedding-v1",
        "text-embedding-async-v2",
        "text-embedding-async-v1",
    ]

    def ndims(self):
        if self.name in [
            "text-embedding-v2",
            "text-embedding-v1",
            "text-embedding-async-v2",
            "text-embedding-async-v1",
        ]:
            return 1536
        elif self.name == "text-embedding-v3":
            # 1,024（默认）、768、512、256、128或64
            return 1024
        elif self.name == "text-embedding-v4":
            # 2,048、1,536、1,024（默认）、768、512、256、128、64
            return 1024
        else:
            raise ValueError(f"Model {self.name} not supported")

    def compute_query_embeddings(
        self, query: Union[str], *args, **kwargs
    ) -> List[np.ndarray]:
        """
        Compute the embeddings for a given user query

        Parameters
        ----------
        query : Union[str, PIL.Image.Image]
            The query to embed. A query can be either text or an image.

        Returns
        -------
            List[np.array]: the list of embeddings
        """
        client = DashscopeEmbeddingFunction._get_client()
        inputs = sanitize_text_input(query)
        result = client.embeddings.create(
            input=inputs,
            model=self.name,
            dimensions=self.ndims,
            encoding_format="float",
        )

        return result.model_dump_json()

        # return [result.embeddings[0]]

    def compute_source_embeddings(
        self, inputs: Union[TEXT, IMAGES], *args, **kwargs
    ) -> List[np.array]:
        """
        Compute the embeddings for the inputs

        Parameters
        ----------
        inputs : Union[TEXT, IMAGES]
            The inputs to embed. The input can be either str, bytes, Path (to an image),
            PIL.Image or list of these.

        Returns
        -------
            List[np.array]: the list of embeddings
        """
        client = DashscopeEmbeddingFunction._get_client()
        inputs = sanitize_text_input(inputs)
        result = client.embeddings.create(
            input=inputs,
            model=self.name,
            dimensions=self.ndims,
            encoding_format="float",
        )

        return result.model_dump_json()

    @staticmethod
    def _get_client():
        if DashscopeEmbeddingFunction.client is None:
            openai = attempt_import_or_raise("openai")
            if os.environ.get("DASHSCOPE_API_KEY") is None:
                api_key_not_found_help("dashscope")
            DashscopeEmbeddingFunction.client = openai.OpenAI(
                api_key=os.getenv("DASHSCOPE_API_KEY"),
                base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",  # 百炼服务的base_url
            )
        return DashscopeEmbeddingFunction.client


if __name__ == "__main__":
    # from llama_index.embeddings.openai import OpenAIEmbedding

    # client = OpenAIEmbedding(
    #     api_key=os.getenv("DASHSCOPE_API_KEY"),
    #     model="text-embedding-v4",
    #     base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",  # 百炼服务的base_url,
    # )

    # client =

    import os
    from llama_index.core import VectorStoreIndex, SimpleDirectoryReader
    from llama_index.core.node_parser import SimpleNodeParser

    # 设置 OpenAI API 密钥
    os.environ["DASHSCOPE_API_KEY"] = "your_openai_api_key"

    # 1. 加载文档
    documents = SimpleDirectoryReader(
        "/Users/<USER>/projects/code_qa/CodebaseV2/docs"
    ).load_data()

    # 2. 创建文档切片器
    node_parser = SimpleNodeParser.from_defaults(
        chunk_size=1024, chunk_overlap=200
    )

    # 3. 将文档切片为节点
    nodes = node_parser.get_nodes_from_documents(documents)

    # from openai import OpenAI

    # 4. 选择嵌入模型
    # client = OpenAI(
    #     api_key=os.getenv(
    #         "DASHSCOPE_API_KEY"
    #     ),  # 如果您没有配置环境变量，请在此处用您的API Key进行替换
    #     base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",  # 百炼服务的base_url
    # )

    # completion = client.embeddings.create(
    #     model="text-embedding-v4",
    #     input="衣服的质量杠杠的，很漂亮，不枉我等了这么久啊，喜欢，以后还来这里买",
    #     dimensions=1024,  # 指定向量维度（仅 text-embedding-v3及 text-embedding-v4支持该参数）
    #     encoding_format="float",
    # )

    from llama_index.embeddings.ollama import OllamaEmbedding

    embed_model = OllamaEmbedding(
        model_name="aroxima/gte-qwen2-1.5b-instruct:latest"
    )

    # 5. 构建向量索引
    index = VectorStoreIndex.from_documents(
        documents,
        node_parser=node_parser,
        embed_model=embed_model,
        show_progress=True,
    )

    # 6. 查询和检索
    retriever = index.as_retriever()
    results = retriever.retrieve("What is neural document search?")
    print(len(results))
    for result in results:
        print(result.node.get_content())  # 打印前100个字符
