{"summary": {"total_queries": 20, "successful_queries": 20, "failed_queries": 0, "success_rate": 1.0, "total_time": 156.57981705665588, "avg_time_per_query": 7.828990852832794}, "aggregated_metrics": {"precision@1": {"mean": 0.09999999999, "min": 0.0, "max": 0.9999999999, "count": 20}, "recall@1": {"mean": 0.09999999999, "min": 0.0, "max": 0.9999999999, "count": 20}, "f1@1": {"mean": 0.09999999999, "min": 0.0, "max": 0.9999999999, "count": 20}, "ndcg@1": {"mean": 0.1, "min": 0.0, "max": 1.0, "count": 20}, "hit_rate@1": {"mean": 0.1, "min": 0.0, "max": 1.0, "count": 20}, "precision@3": {"mean": 0.18333333332055557, "min": 0.0, "max": 0.9999999999, "count": 20}, "recall@3": {"mean": 0.2749999999762499, "min": 0.0, "max": 0.9999999999, "count": 20}, "f1@3": {"mean": 0.20999999998510002, "min": 0.0, "max": 0.9999999999, "count": 20}, "ndcg@3": {"mean": 0.40735293468371186, "min": 0.0, "max": 2.6309297535714578, "count": 20}, "hit_rate@3": {"mean": 0.35, "min": 0.0, "max": 1.0, "count": 20}, "precision@5": {"mean": 0.1491666666585236, "min": 0.0, "max": 0.9999999999, "count": 20}, "recall@5": {"mean": 0.29999999997499993, "min": 0.0, "max": 0.99999999995, "count": 20}, "f1@5": {"mean": 0.19023809522689555, "min": 0.0, "max": 0.9999999999, "count": 20}, "ndcg@5": {"mean": 0.5310109455939174, "min": 0.0, "max": 3.5616063116448506, "count": 20}, "hit_rate@5": {"mean": 0.35, "min": 0.0, "max": 1.0, "count": 20}, "precision@10": {"mean": 0.10208333333162078, "min": 0.0, "max": 0.2857142857102041, "count": 20}, "recall@10": {"mean": 0.49999999995749994, "min": 0.0, "max": 0.99999999995, "count": 20}, "f1@10": {"mean": 0.1670634920589305, "min": 0.0, "max": 0.4444444444345679, "count": 20}, "ndcg@10": {"mean": 0.7097257734842082, "min": 0.0, "max": 4.281792452212725, "count": 20}, "hit_rate@10": {"mean": 0.55, "min": 0.0, "max": 1.0, "count": 20}, "map": {"mean": 0.8219345238095238, "min": 0.0, "max": 6.875, "count": 20}, "mrr": {"mean": 0.23208333333333334, "min": 0.0, "max": 1.0, "count": 20}}, "detailed_results": [{"query_id": "req_001", "query_text": "如何发送HTTP GET请求", "metrics": {"precision@1": 0.0, "recall@1": 0.0, "f1@1": 0.0, "ndcg@1": 0.0, "hit_rate@1": 0.0, "precision@3": 0.3333333333222222, "recall@3": 0.499999999975, "f1@3": 0.399999999984, "ndcg@3": 0.4444444444444445, "hit_rate@3": 1.0, "precision@5": 0.24999999999375, "recall@5": 0.499999999975, "f1@5": 0.3333333333222222, "ndcg@5": 0.4444444444444445, "hit_rate@5": 1.0, "precision@10": 0.2857142857102041, "recall@10": 0.99999999995, "f1@10": 0.4444444444345679, "ndcg@10": 0.6296296296296297, "hit_rate@10": 1.0, "map": 0.375, "mrr": 0.5}, "retrieved_count": 10, "ground_truth_count": 2, "success": true}, {"query_id": "req_002", "query_text": "如何处理HTTP响应", "metrics": {"precision@1": 0.0, "recall@1": 0.0, "f1@1": 0.0, "ndcg@1": 0.0, "hit_rate@1": 0.0, "precision@3": 0.3333333333222222, "recall@3": 0.9999999999, "f1@3": 0.499999999975, "ndcg@3": 1.0, "hit_rate@3": 1.0, "precision@5": 0.24999999999375, "recall@5": 0.9999999999, "f1@5": 0.39999999998400004, "ndcg@5": 1.5, "hit_rate@5": 1.0, "precision@10": 0.1249999999984375, "recall@10": 0.9999999999, "f1@10": 0.22222222221728397, "ndcg@10": 1.8562071871080221, "hit_rate@10": 1.0, "map": 1.4285714285714286, "mrr": 0.5}, "retrieved_count": 10, "ground_truth_count": 1, "success": true}, {"query_id": "req_003", "query_text": "Session会话管理的实现", "metrics": {"precision@1": 0.9999999999, "recall@1": 0.9999999999, "f1@1": 0.9999999999, "ndcg@1": 1.0, "hit_rate@1": 1.0, "precision@3": 0.9999999999, "recall@3": 0.9999999999, "f1@3": 0.9999999999, "ndcg@3": 2.6309297535714578, "hit_rate@3": 1.0, "precision@5": 0.499999999975, "recall@5": 0.9999999999, "f1@5": 0.6666666666222222, "ndcg@5": 3.1309297535714578, "hit_rate@5": 1.0, "precision@10": 0.24999999999375, "recall@10": 0.9999999999, "f1@10": 0.39999999998400004, "ndcg@10": 3.8188125564699806, "hit_rate@10": 1.0, "map": 5.433333333333333, "mrr": 1.0}, "retrieved_count": 10, "ground_truth_count": 1, "success": true}, {"query_id": "req_004", "query_text": "HTTP适配器的作用", "metrics": {"precision@1": 0.0, "recall@1": 0.0, "f1@1": 0.0, "ndcg@1": 0.0, "hit_rate@1": 0.0, "precision@3": 0.0, "recall@3": 0.0, "f1@3": 0.0, "ndcg@3": 0.0, "hit_rate@3": 0.0, "precision@5": 0.0, "recall@5": 0.0, "f1@5": 0.0, "ndcg@5": 0.0, "hit_rate@5": 0.0, "precision@10": 0.14285714285510204, "recall@10": 0.9999999999, "f1@10": 0.24999999999375, "ndcg@10": 0.3333333333333333, "hit_rate@10": 1.0, "map": 0.125, "mrr": 0.125}, "retrieved_count": 10, "ground_truth_count": 1, "success": true}, {"query_id": "req_005", "query_text": "如何处理认证", "metrics": {"precision@1": 0.0, "recall@1": 0.0, "f1@1": 0.0, "ndcg@1": 0.0, "hit_rate@1": 0.0, "precision@3": 0.0, "recall@3": 0.0, "f1@3": 0.0, "ndcg@3": 0.0, "hit_rate@3": 0.0, "precision@5": 0.0, "recall@5": 0.0, "f1@5": 0.0, "ndcg@5": 0.0, "hit_rate@5": 0.0, "precision@10": 0.0, "recall@10": 0.0, "f1@10": 0.0, "ndcg@10": 0.0, "hit_rate@10": 0.0, "map": 0.0, "mrr": 0.0}, "retrieved_count": 10, "ground_truth_count": 1, "success": true}, {"query_id": "req_006", "query_text": "Cookie处理机制", "metrics": {"precision@1": 0.9999999999, "recall@1": 0.9999999999, "f1@1": 0.9999999999, "ndcg@1": 1.0, "hit_rate@1": 1.0, "precision@3": 0.9999999999, "recall@3": 0.9999999999, "f1@3": 0.9999999999, "ndcg@3": 2.6309297535714578, "hit_rate@3": 1.0, "precision@5": 0.9999999999, "recall@5": 0.9999999999, "f1@5": 0.9999999999, "ndcg@5": 3.5616063116448506, "hit_rate@5": 1.0, "precision@10": 0.24999999999375, "recall@10": 0.9999999999, "f1@10": 0.39999999998400004, "ndcg@10": 4.281792452212725, "hit_rate@10": 1.0, "map": 6.875, "mrr": 1.0}, "retrieved_count": 10, "ground_truth_count": 1, "success": true}, {"query_id": "req_007", "query_text": "异常处理和错误类型", "metrics": {"precision@1": 0.0, "recall@1": 0.0, "f1@1": 0.0, "ndcg@1": 0.0, "hit_rate@1": 0.0, "precision@3": 0.0, "recall@3": 0.0, "f1@3": 0.0, "ndcg@3": 0.0, "hit_rate@3": 0.0, "precision@5": 0.0, "recall@5": 0.0, "f1@5": 0.0, "ndcg@5": 0.0, "hit_rate@5": 0.0, "precision@10": 0.0, "recall@10": 0.0, "f1@10": 0.0, "ndcg@10": 0.0, "hit_rate@10": 0.0, "map": 0.0, "mrr": 0.0}, "retrieved_count": 10, "ground_truth_count": 1, "success": true}, {"query_id": "req_008", "query_text": "如何发送POST请求", "metrics": {"precision@1": 0.0, "recall@1": 0.0, "f1@1": 0.0, "ndcg@1": 0.0, "hit_rate@1": 0.0, "precision@3": 0.3333333333222222, "recall@3": 0.9999999999, "f1@3": 0.499999999975, "ndcg@3": 0.6309297535714575, "hit_rate@3": 1.0, "precision@5": 0.3333333333222222, "recall@5": 0.9999999999, "f1@5": 0.499999999975, "ndcg@5": 0.6309297535714575, "hit_rate@5": 1.0, "precision@10": 0.16666666666388888, "recall@10": 0.9999999999, "f1@10": 0.2857142857061224, "ndcg@10": 0.6309297535714575, "hit_rate@10": 1.0, "map": 0.3333333333333333, "mrr": 0.3333333333333333}, "retrieved_count": 10, "ground_truth_count": 1, "success": true}, {"query_id": "req_009", "query_text": "工具函数和辅助方法", "metrics": {"precision@1": 0.0, "recall@1": 0.0, "f1@1": 0.0, "ndcg@1": 0.0, "hit_rate@1": 0.0, "precision@3": 0.0, "recall@3": 0.0, "f1@3": 0.0, "ndcg@3": 0.0, "hit_rate@3": 0.0, "precision@5": 0.0, "recall@5": 0.0, "f1@5": 0.0, "ndcg@5": 0.0, "hit_rate@5": 0.0, "precision@10": 0.1249999999984375, "recall@10": 0.9999999999, "f1@10": 0.22222222221728397, "ndcg@10": 0.3010299956639812, "hit_rate@10": 1.0, "map": 0.1, "mrr": 0.1}, "retrieved_count": 10, "ground_truth_count": 1, "success": true}, {"query_id": "req_010", "query_text": "HTTP状态码处理", "metrics": {"precision@1": 0.0, "recall@1": 0.0, "f1@1": 0.0, "ndcg@1": 0.0, "hit_rate@1": 0.0, "precision@3": 0.0, "recall@3": 0.0, "f1@3": 0.0, "ndcg@3": 0.0, "hit_rate@3": 0.0, "precision@5": 0.0, "recall@5": 0.0, "f1@5": 0.0, "ndcg@5": 0.0, "hit_rate@5": 0.0, "precision@10": 0.14285714285510204, "recall@10": 0.9999999999, "f1@10": 0.24999999999375, "ndcg@10": 0.3333333333333333, "hit_rate@10": 1.0, "map": 0.125, "mrr": 0.125}, "retrieved_count": 10, "ground_truth_count": 1, "success": true}, {"query_id": "req_011", "query_text": "数据结构和容器类", "metrics": {"precision@1": 0.0, "recall@1": 0.0, "f1@1": 0.0, "ndcg@1": 0.0, "hit_rate@1": 0.0, "precision@3": 0.0, "recall@3": 0.0, "f1@3": 0.0, "ndcg@3": 0.0, "hit_rate@3": 0.0, "precision@5": 0.0, "recall@5": 0.0, "f1@5": 0.0, "ndcg@5": 0.0, "hit_rate@5": 0.0, "precision@10": 0.0, "recall@10": 0.0, "f1@10": 0.0, "ndcg@10": 0.0, "hit_rate@10": 0.0, "map": 0.0, "mrr": 0.0}, "retrieved_count": 10, "ground_truth_count": 1, "success": true}, {"query_id": "req_012", "query_text": "钩子函数机制", "metrics": {"precision@1": 0.0, "recall@1": 0.0, "f1@1": 0.0, "ndcg@1": 0.0, "hit_rate@1": 0.0, "precision@3": 0.0, "recall@3": 0.0, "f1@3": 0.0, "ndcg@3": 0.0, "hit_rate@3": 0.0, "precision@5": 0.0, "recall@5": 0.0, "f1@5": 0.0, "ndcg@5": 0.0, "hit_rate@5": 0.0, "precision@10": 0.0, "recall@10": 0.0, "f1@10": 0.0, "ndcg@10": 0.0, "hit_rate@10": 0.0, "map": 0.0, "mrr": 0.0}, "retrieved_count": 10, "ground_truth_count": 1, "success": true}, {"query_id": "req_013", "query_text": "兼容性处理代码", "metrics": {"precision@1": 0.0, "recall@1": 0.0, "f1@1": 0.0, "ndcg@1": 0.0, "hit_rate@1": 0.0, "precision@3": 0.0, "recall@3": 0.0, "f1@3": 0.0, "ndcg@3": 0.0, "hit_rate@3": 0.0, "precision@5": 0.0, "recall@5": 0.0, "f1@5": 0.0, "ndcg@5": 0.0, "hit_rate@5": 0.0, "precision@10": 0.0, "recall@10": 0.0, "f1@10": 0.0, "ndcg@10": 0.0, "hit_rate@10": 0.0, "map": 0.0, "mrr": 0.0}, "retrieved_count": 10, "ground_truth_count": 1, "success": true}, {"query_id": "req_014", "query_text": "SSL证书处理", "metrics": {"precision@1": 0.0, "recall@1": 0.0, "f1@1": 0.0, "ndcg@1": 0.0, "hit_rate@1": 0.0, "precision@3": 0.0, "recall@3": 0.0, "f1@3": 0.0, "ndcg@3": 0.0, "hit_rate@3": 0.0, "precision@5": 0.0, "recall@5": 0.0, "f1@5": 0.0, "ndcg@5": 0.0, "hit_rate@5": 0.0, "precision@10": 0.0, "recall@10": 0.0, "f1@10": 0.0, "ndcg@10": 0.0, "hit_rate@10": 0.0, "map": 0.0, "mrr": 0.0}, "retrieved_count": 10, "ground_truth_count": 1, "success": true}, {"query_id": "req_015", "query_text": "如何设置请求头", "metrics": {"precision@1": 0.0, "recall@1": 0.0, "f1@1": 0.0, "ndcg@1": 0.0, "hit_rate@1": 0.0, "precision@3": 0.3333333333222222, "recall@3": 0.499999999975, "f1@3": 0.399999999984, "ndcg@3": 0.5294117647058824, "hit_rate@3": 1.0, "precision@5": 0.24999999999375, "recall@5": 0.499999999975, "f1@5": 0.3333333333222222, "ndcg@5": 0.7941176470588235, "hit_rate@5": 1.0, "precision@10": 0.14285714285510204, "recall@10": 0.499999999975, "f1@10": 0.22222222221728394, "ndcg@10": 1.1420667438204724, "hit_rate@10": 1.0, "map": 0.9142857142857144, "mrr": 0.5}, "retrieved_count": 10, "ground_truth_count": 2, "success": true}, {"query_id": "req_016", "query_text": "超时设置和处理", "metrics": {"precision@1": 0.0, "recall@1": 0.0, "f1@1": 0.0, "ndcg@1": 0.0, "hit_rate@1": 0.0, "precision@3": 0.0, "recall@3": 0.0, "f1@3": 0.0, "ndcg@3": 0.0, "hit_rate@3": 0.0, "precision@5": 0.0, "recall@5": 0.0, "f1@5": 0.0, "ndcg@5": 0.0, "hit_rate@5": 0.0, "precision@10": 0.0, "recall@10": 0.0, "f1@10": 0.0, "ndcg@10": 0.0, "hit_rate@10": 0.0, "map": 0.0, "mrr": 0.0}, "retrieved_count": 10, "ground_truth_count": 1, "success": true}, {"query_id": "req_017", "query_text": "代理服务器配置", "metrics": {"precision@1": 0.0, "recall@1": 0.0, "f1@1": 0.0, "ndcg@1": 0.0, "hit_rate@1": 0.0, "precision@3": 0.0, "recall@3": 0.0, "f1@3": 0.0, "ndcg@3": 0.0, "hit_rate@3": 0.0, "precision@5": 0.0, "recall@5": 0.0, "f1@5": 0.0, "ndcg@5": 0.0, "hit_rate@5": 0.0, "precision@10": 0.1249999999984375, "recall@10": 0.499999999975, "f1@10": 0.19999999999600002, "ndcg@10": 0.1372549019607843, "hit_rate@10": 1.0, "map": 0.0625, "mrr": 0.125}, "retrieved_count": 10, "ground_truth_count": 2, "success": true}, {"query_id": "req_018", "query_text": "文件上传功能", "metrics": {"precision@1": 0.0, "recall@1": 0.0, "f1@1": 0.0, "ndcg@1": 0.0, "hit_rate@1": 0.0, "precision@3": 0.0, "recall@3": 0.0, "f1@3": 0.0, "ndcg@3": 0.0, "hit_rate@3": 0.0, "precision@5": 0.0, "recall@5": 0.0, "f1@5": 0.0, "ndcg@5": 0.0, "hit_rate@5": 0.0, "precision@10": 0.0, "recall@10": 0.0, "f1@10": 0.0, "ndcg@10": 0.0, "hit_rate@10": 0.0, "map": 0.0, "mrr": 0.0}, "retrieved_count": 10, "ground_truth_count": 1, "success": true}, {"query_id": "req_019", "query_text": "重定向处理机制", "metrics": {"precision@1": 0.0, "recall@1": 0.0, "f1@1": 0.0, "ndcg@1": 0.0, "hit_rate@1": 0.0, "precision@3": 0.0, "recall@3": 0.0, "f1@3": 0.0, "ndcg@3": 0.0, "hit_rate@3": 0.0, "precision@5": 0.0, "recall@5": 0.0, "f1@5": 0.0, "ndcg@5": 0.0, "hit_rate@5": 0.0, "precision@10": 0.0, "recall@10": 0.0, "f1@10": 0.0, "ndcg@10": 0.0, "hit_rate@10": 0.0, "map": 0.0, "mrr": 0.0}, "retrieved_count": 10, "ground_truth_count": 1, "success": true}, {"query_id": "req_020", "query_text": "编码和解码处理", "metrics": {"precision@1": 0.0, "recall@1": 0.0, "f1@1": 0.0, "ndcg@1": 0.0, "hit_rate@1": 0.0, "precision@3": 0.3333333333222222, "recall@3": 0.499999999975, "f1@3": 0.399999999984, "ndcg@3": 0.28041322380953665, "hit_rate@3": 1.0, "precision@5": 0.399999999992, "recall@5": 0.99999999995, "f1@5": 0.5714285714122448, "ndcg@5": 0.5581910015873145, "hit_rate@5": 1.0, "precision@10": 0.2857142857102041, "recall@10": 0.99999999995, "f1@10": 0.4444444444345679, "ndcg@10": 0.7301255825804441, "hit_rate@10": 1.0, "map": 0.6666666666666666, "mrr": 0.3333333333333333}, "retrieved_count": 10, "ground_truth_count": 2, "success": true}], "timestamp": "2025-06-25 13:32:17"}