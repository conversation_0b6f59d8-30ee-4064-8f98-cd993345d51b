"""
本地Pipeline实现
使用本地模型的检索管道
"""
from typing import List, Dict, Any
from local_retriever import <PERSON><PERSON>25Retriever, LocalEmbeddingRetriever
from local_model_adapter import LocalLLMClient
from utils import setup_logger

logger = setup_logger(__name__)


class LocalPipeline:
    """本地Pipeline"""
    
    def __init__(
        self,
        bm25_persist_path: str = "temp/bm25_test",
        emb_persist_path: str = "temp/embedding_test",
        enable_rerank: bool = False  # 暂时禁用重排序
    ):
        self.bm25_retriever = LocalBM25Retriever(bm25_persist_path)
        self.emb_retriever = LocalEmbeddingRetriever(emb_persist_path)
        self.enable_rerank = enable_rerank
        
        if enable_rerank:
            try:
                self.llm_client = LocalLLMClient()
            except Exception as e:
                logger.warning(f"LLM客户端初始化失败，禁用重排序: {e}")
                self.enable_rerank = False
    
    def run(self, query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """运行检索"""
        logger.info(f"开始检索查询: '{query}'")
        
        # Step 1: BM25检索
        bm25_results = self.bm25_retriever.retrieve(query, top_k=top_k*2)
        logger.info(f"BM25检索到 {len(bm25_results)} 条结果")
        
        # Step 2: Embedding检索
        emb_results = self.emb_retriever.retrieve(query, top_k=top_k*2)
        logger.info(f"Embedding检索到 {len(emb_results)} 条结果")
        
        # Step 3: 合并结果
        all_results = []
        
        # 添加BM25结果（权重0.4）
        for result in bm25_results:
            all_results.append({
                "text": result["text"],
                "score": result["score"] * 0.4,
                "metadata": result["metadata"],
                "file_path": result["metadata"]["file_path"],
                "source": "bm25"
            })
        
        # 添加Embedding结果（权重0.6）
        for result in emb_results:
            all_results.append({
                "text": result["text"],
                "score": result["score"] * 0.6,
                "metadata": result["metadata"],
                "file_path": result["metadata"]["file_path"],
                "source": "embedding"
            })
        
        # Step 4: 去重（基于文件路径和起始行）
        seen = set()
        unique_results = []
        
        for result in all_results:
            key = (result["file_path"], result["metadata"].get("start_line", 0))
            if key not in seen:
                seen.add(key)
                unique_results.append(result)
        
        # Step 5: 按分数排序
        unique_results.sort(key=lambda x: x["score"], reverse=True)
        
        # Step 6: 返回top_k结果
        final_results = unique_results[:top_k]
        
        logger.info(f"最终返回 {len(final_results)} 条结果")
        return final_results


def test_pipeline():
    """测试Pipeline"""
    print("测试本地Pipeline...")
    
    pipeline = LocalPipeline()
    
    test_queries = [
        "如何发送HTTP GET请求",
        "Session会话管理",
        "HTTP响应处理",
        "认证机制",
        "异常处理"
    ]
    
    for query in test_queries:
        print(f"\n查询: {query}")
        results = pipeline.run(query, top_k=3)
        
        for i, result in enumerate(results, 1):
            print(f"  {i}. {result['file_path']} (score: {result['score']:.3f}, source: {result['source']})")
            print(f"     {result['text'][:100]}...")


if __name__ == "__main__":
    test_pipeline()
