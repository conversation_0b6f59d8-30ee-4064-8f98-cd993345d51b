"""
适配本地模型的检索器
"""
import os
import pickle
import numpy as np
from typing import List, Dict, Any, Optional
from rank_bm25 import BM25Okapi
import faiss
from local_model_adapter import LocalEmbeddingModel
from utils import setup_logger

logger = setup_logger(__name__)


class LocalBM25Retriever:
    """本地BM25检索器"""
    
    def __init__(self, persist_path: str):
        self.persist_path = persist_path
        self.bm25 = None
        self.documents = []
        self.metadata = []
        self.load_index()
    
    def load_index(self):
        """加载索引"""
        try:
            with open(os.path.join(self.persist_path, "bm25_index.pkl"), "rb") as f:
                data = pickle.load(f)
                self.bm25 = data["bm25"]
                self.documents = data["documents"]
                self.metadata = data["metadata"]
            logger.info(f"BM25索引加载成功，文档数量: {len(self.documents)}")
        except Exception as e:
            logger.error(f"BM25索引加载失败: {e}")
            self.build_index()
    
    def build_index(self):
        """构建索引"""
        logger.info("开始构建BM25索引...")
        
        # 扫描代码文件
        repo_path = os.environ.get("REPO_PATH", "../test_repo")
        documents = []
        metadata = []
        
        for root, dirs, files in os.walk(repo_path):
            # 跳过隐藏目录和常见的忽略目录
            dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['__pycache__', 'node_modules', '.git']]
            
            for file in files:
                if file.endswith(('.py', '.js', '.java', '.cpp', '.c', '.h')):
                    file_path = os.path.join(root, file)
                    rel_path = os.path.relpath(file_path, repo_path)
                    
                    try:
                        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                            content = f.read()
                        
                        # 按行分割并创建文档块
                        lines = content.split('\n')
                        chunk_size = 50  # 每50行一个块
                        
                        for i in range(0, len(lines), chunk_size):
                            chunk_lines = lines[i:i+chunk_size]
                            chunk_text = '\n'.join(chunk_lines)
                            
                            if chunk_text.strip():
                                documents.append(chunk_text)
                                metadata.append({
                                    "file_path": rel_path,
                                    "start_line": i + 1,
                                    "end_line": min(i + chunk_size, len(lines)),
                                    "chunk_id": len(documents)
                                })
                    
                    except Exception as e:
                        logger.warning(f"读取文件失败 {rel_path}: {e}")
                        continue
        
        # 构建BM25索引
        tokenized_docs = [doc.split() for doc in documents]
        self.bm25 = BM25Okapi(tokenized_docs)
        self.documents = documents
        self.metadata = metadata
        
        # 保存索引
        os.makedirs(self.persist_path, exist_ok=True)
        with open(os.path.join(self.persist_path, "bm25_index.pkl"), "wb") as f:
            pickle.dump({
                "bm25": self.bm25,
                "documents": self.documents,
                "metadata": self.metadata
            }, f)
        
        logger.info(f"BM25索引构建完成，文档数量: {len(documents)}")
    
    def retrieve(self, query: str, top_k: int = 10) -> List[Dict[str, Any]]:
        """检索"""
        if not self.bm25:
            return []
        
        tokenized_query = query.split()
        scores = self.bm25.get_scores(tokenized_query)
        
        # 获取top_k结果
        top_indices = np.argsort(scores)[::-1][:top_k]
        
        results = []
        for idx in top_indices:
            if scores[idx] > 0:
                results.append({
                    "text": self.documents[idx],
                    "score": float(scores[idx]),
                    "metadata": self.metadata[idx]
                })
        
        return results


class LocalEmbeddingRetriever:
    """本地Embedding检索器"""
    
    def __init__(self, persist_path: str):
        self.persist_path = persist_path
        self.embedding_model = LocalEmbeddingModel()
        self.index = None
        self.documents = []
        self.metadata = []
        self.load_index()
    
    def load_index(self):
        """加载索引"""
        try:
            # 加载FAISS索引
            self.index = faiss.read_index(os.path.join(self.persist_path, "embedding_index.faiss"))
            
            # 加载文档和元数据
            with open(os.path.join(self.persist_path, "embedding_docs.pkl"), "rb") as f:
                data = pickle.load(f)
                self.documents = data["documents"]
                self.metadata = data["metadata"]
            
            logger.info(f"Embedding索引加载成功，文档数量: {len(self.documents)}")
        except Exception as e:
            logger.error(f"Embedding索引加载失败: {e}")
            self.build_index()
    
    def build_index(self):
        """构建索引"""
        logger.info("开始构建Embedding索引...")
        
        # 扫描代码文件（复用BM25的逻辑）
        repo_path = os.environ.get("REPO_PATH", "../test_repo")
        documents = []
        metadata = []
        
        for root, dirs, files in os.walk(repo_path):
            dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['__pycache__', 'node_modules', '.git']]
            
            for file in files:
                if file.endswith(('.py', '.js', '.java', '.cpp', '.c', '.h')):
                    file_path = os.path.join(root, file)
                    rel_path = os.path.relpath(file_path, repo_path)
                    
                    try:
                        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                            content = f.read()
                        
                        lines = content.split('\n')
                        chunk_size = 50
                        
                        for i in range(0, len(lines), chunk_size):
                            chunk_lines = lines[i:i+chunk_size]
                            chunk_text = '\n'.join(chunk_lines)
                            
                            if chunk_text.strip():
                                documents.append(chunk_text)
                                metadata.append({
                                    "file_path": rel_path,
                                    "start_line": i + 1,
                                    "end_line": min(i + chunk_size, len(lines)),
                                    "chunk_id": len(documents)
                                })
                    
                    except Exception as e:
                        logger.warning(f"读取文件失败 {rel_path}: {e}")
                        continue
        
        # 生成embeddings
        logger.info("生成文档embeddings...")
        embeddings = self.embedding_model.encode(documents)
        
        # 构建FAISS索引
        dimension = embeddings.shape[1]
        self.index = faiss.IndexFlatIP(dimension)  # 内积相似度
        
        # 归一化embeddings以使用余弦相似度
        faiss.normalize_L2(embeddings)
        self.index.add(embeddings.astype('float32'))
        
        self.documents = documents
        self.metadata = metadata
        
        # 保存索引
        os.makedirs(self.persist_path, exist_ok=True)
        faiss.write_index(self.index, os.path.join(self.persist_path, "embedding_index.faiss"))
        
        with open(os.path.join(self.persist_path, "embedding_docs.pkl"), "wb") as f:
            pickle.dump({
                "documents": self.documents,
                "metadata": self.metadata
            }, f)
        
        logger.info(f"Embedding索引构建完成，文档数量: {len(documents)}")
    
    def retrieve(self, query: str, top_k: int = 10) -> List[Dict[str, Any]]:
        """检索"""
        if not self.index:
            return []
        
        # 生成查询embedding
        query_embedding = self.embedding_model.encode([query])
        faiss.normalize_L2(query_embedding)
        
        # 搜索
        scores, indices = self.index.search(query_embedding.astype('float32'), top_k)
        
        results = []
        for score, idx in zip(scores[0], indices[0]):
            if idx != -1:  # 有效索引
                results.append({
                    "text": self.documents[idx],
                    "score": float(score),
                    "metadata": self.metadata[idx]
                })
        
        return results


def test_retrievers():
    """测试检索器"""
    print("测试本地检索器...")
    
    # 测试BM25
    bm25_path = "temp/bm25_test"
    bm25_retriever = LocalBM25Retriever(bm25_path)
    
    query = "HTTP GET request"
    results = bm25_retriever.retrieve(query, top_k=3)
    print(f"BM25检索结果数量: {len(results)}")
    
    # 测试Embedding
    emb_path = "temp/embedding_test"
    emb_retriever = LocalEmbeddingRetriever(emb_path)
    
    results = emb_retriever.retrieve(query, top_k=3)
    print(f"Embedding检索结果数量: {len(results)}")


if __name__ == "__main__":
    test_retrievers()
