#!/bin/bash

# Codebase QA 基准测试启动脚本
# 使用方法: ./run_benchmark.sh [选项]

set -e

# 默认配置
REPO_PATH=""
OUTPUT_DIR="./benchmark_results"
PERSIST_PATH="./temp"
DATASET_TYPE="auto"
PIPELINES="pro"
MAX_QUERIES=50
USE_LLM=false
API_KEY=""

# 显示帮助信息
show_help() {
    cat << EOF
Codebase QA 基准测试启动脚本

使用方法:
    $0 [选项]

选项:
    -r, --repo-path PATH        代码库路径 (必需)
    -o, --output-dir DIR        输出目录 (默认: ./benchmark_results)
    -p, --persist-path DIR      索引持久化路径 (默认: ./temp)
    -d, --dataset-type TYPE     数据集类型: auto|intelligent|predefined (默认: auto)
    -P, --pipelines TYPES       Pipeline类型: simple|pro|balance|best|all (默认: pro)
    -m, --max-queries NUM       最大查询数量 (默认: 50)
    -l, --use-llm              使用LLM生成查询
    -k, --api-key KEY          OpenAI API密钥
    -h, --help                 显示此帮助信息

示例:
    # 基础评测
    $0 -r /path/to/repo

    # 评测所有Pipeline
    $0 -r /path/to/repo -P all

    # 使用LLM生成高质量数据集
    $0 -r /path/to/repo -d intelligent -l -k your_api_key

    # 自定义配置
    $0 -r /path/to/repo -o ./my_results -m 100 -P "pro balance best"
EOF
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -r|--repo-path)
            REPO_PATH="$2"
            shift 2
            ;;
        -o|--output-dir)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        -p|--persist-path)
            PERSIST_PATH="$2"
            shift 2
            ;;
        -d|--dataset-type)
            DATASET_TYPE="$2"
            shift 2
            ;;
        -P|--pipelines)
            PIPELINES="$2"
            shift 2
            ;;
        -m|--max-queries)
            MAX_QUERIES="$2"
            shift 2
            ;;
        -l|--use-llm)
            USE_LLM=true
            shift
            ;;
        -k|--api-key)
            API_KEY="$2"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            echo "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 验证必需参数
if [[ -z "$REPO_PATH" ]]; then
    echo "错误: 必须指定代码库路径"
    echo "使用 -h 查看帮助信息"
    exit 1
fi

if [[ ! -d "$REPO_PATH" ]]; then
    echo "错误: 代码库路径不存在: $REPO_PATH"
    exit 1
fi

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到python3"
    exit 1
fi

# 检查依赖
echo "检查Python依赖..."
python3 -c "
import sys
required_modules = ['pydantic', 'numpy']
missing_modules = []

for module in required_modules:
    try:
        __import__(module)
    except ImportError:
        missing_modules.append(module)

if missing_modules:
    print(f'错误: 缺少依赖模块: {missing_modules}')
    print('请运行: pip install -r requirements.txt')
    sys.exit(1)
else:
    print('依赖检查通过')
"

if [[ $? -ne 0 ]]; then
    exit 1
fi

# 构建命令行参数
PYTHON_ARGS=(
    "--repo-path" "$REPO_PATH"
    "--output-dir" "$OUTPUT_DIR"
    "--persist-path" "$PERSIST_PATH"
    "--dataset-type" "$DATASET_TYPE"
    "--max-queries" "$MAX_QUERIES"
)

# 处理Pipeline参数
IFS=' ' read -ra PIPELINE_ARRAY <<< "$PIPELINES"
PYTHON_ARGS+=("--pipelines")
for pipeline in "${PIPELINE_ARRAY[@]}"; do
    PYTHON_ARGS+=("$pipeline")
done

# 处理LLM参数
if [[ "$USE_LLM" == "true" ]]; then
    PYTHON_ARGS+=("--use-llm")
    if [[ -n "$API_KEY" ]]; then
        PYTHON_ARGS+=("--api-key" "$API_KEY")
    fi
fi

# 显示配置信息
echo "=================================="
echo "Codebase QA 基准测试"
echo "=================================="
echo "代码库路径: $REPO_PATH"
echo "输出目录: $OUTPUT_DIR"
echo "索引路径: $PERSIST_PATH"
echo "数据集类型: $DATASET_TYPE"
echo "Pipeline: $PIPELINES"
echo "最大查询数: $MAX_QUERIES"
echo "使用LLM: $USE_LLM"
if [[ -n "$API_KEY" ]]; then
    echo "API密钥: ${API_KEY:0:8}..."
fi
echo "=================================="

# 创建输出目录
mkdir -p "$OUTPUT_DIR"

# 检查索引文件
echo "检查索引文件..."
REPO_NAME=$(basename "$REPO_PATH")
INDEX_PATHS=(
    "$PERSIST_PATH/bm25_index_pro/$REPO_NAME"
    "$PERSIST_PATH/embedding_index_pro/$REPO_NAME"
)

missing_indexes=()
for index_path in "${INDEX_PATHS[@]}"; do
    if [[ ! -d "$index_path" ]]; then
        missing_indexes+=("$index_path")
    fi
done

if [[ ${#missing_indexes[@]} -gt 0 ]]; then
    echo "警告: 以下索引文件不存在:"
    for index in "${missing_indexes[@]}"; do
        echo "  - $index"
    done
    echo "请先构建索引文件"
    echo "提示: 运行 bash scripts/pro/build_index.sh"
    
    read -p "是否继续运行? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "已取消"
        exit 1
    fi
fi

# 运行基准测试
echo "开始运行基准测试..."
echo "命令: python3 benchmark/run_evaluation.py ${PYTHON_ARGS[*]}"
echo

# 切换到脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 执行Python脚本
python3 benchmark/run_evaluation.py "${PYTHON_ARGS[@]}"

# 检查执行结果
if [[ $? -eq 0 ]]; then
    echo
    echo "=================================="
    echo "基准测试完成!"
    echo "结果保存在: $OUTPUT_DIR"
    echo "=================================="
    
    # 显示结果文件
    if [[ -d "$OUTPUT_DIR" ]]; then
        echo "生成的文件:"
        find "$OUTPUT_DIR" -name "*.json" -type f | head -10 | while read -r file; do
            echo "  - $file"
        done
    fi
else
    echo
    echo "=================================="
    echo "基准测试失败!"
    echo "请检查错误信息并重试"
    echo "=================================="
    exit 1
fi
