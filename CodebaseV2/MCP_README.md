# Codebase QA MCP Server

基于Model Context Protocol (MCP)的代码库问答服务器，支持自然语言查询和代码检索。

## 功能特性

- 🔍 **智能代码搜索**: 使用自然语言查询代码库中的相关片段
- 📁 **文件内容获取**: 获取指定文件的完整内容
- 🏗️ **代码结构分析**: 分析代码文件的类、函数等结构
- 🔧 **多种Pipeline**: 支持simple、pro、balance、best四种检索策略
- 🌐 **多种传输方式**: 支持stdio、SSE、Streamable HTTP传输
- 📊 **资源监控**: 提供配置信息和统计数据

## 安装依赖

```bash
# 安装MCP Python SDK
pip install mcp

# 安装项目依赖
pip install -r requirements.txt
```

## 快速开始

### 1. 准备索引

首先需要为你的代码库构建索引：

```bash
# 设置环境变量
export REPO_PATH="/path/to/your/repository"
export PERSIST_PATH="./temp"

# 构建索引（根据需要选择）
bash scripts/pro/build_index.sh
```

### 2. 启动MCP服务器

#### 方式一：使用启动脚本（推荐）

```bash
# 基本启动
python start_mcp_server.py --repo-path /path/to/your/repo

# 指定Pipeline类型
python start_mcp_server.py --repo-path /path/to/your/repo --pipeline-type pro

# 使用HTTP传输
python start_mcp_server.py --repo-path /path/to/your/repo --transport sse --port 3000
```

#### 方式二：直接运行

```bash
# 设置环境变量
export REPO_PATH="/path/to/your/repository"
export PERSIST_PATH="./temp"
export PIPELINE_TYPE="pro"

# 运行FastMCP服务器
python mcp_server_fast.py

# 或运行标准MCP服务器
python mcp_server.py
```

### 3. 配置Claude Desktop

将以下配置添加到Claude Desktop的配置文件中：

**macOS**: `~/Library/Application Support/Claude/claude_desktop_config.json`
**Windows**: `%APPDATA%\Claude\claude_desktop_config.json`

```json
{
  "mcpServers": {
    "codebase-qa": {
      "command": "python",
      "args": [
        "/path/to/CodebaseV2/mcp_server_fast.py"
      ],
      "env": {
        "REPO_PATH": "/path/to/your/repository",
        "PERSIST_PATH": "/path/to/CodebaseV2/temp",
        "PIPELINE_TYPE": "pro"
      }
    }
  }
}
```

## 使用方法

### 可用工具

1. **search_codebase**: 搜索代码库
   ```
   搜索与"HTTP请求处理"相关的代码
   ```

2. **get_file_content**: 获取文件内容
   ```
   获取 src/main.py 的完整内容
   ```

3. **analyze_code_structure**: 分析代码结构
   ```
   分析 src/models.py 的代码结构
   ```

### 可用资源

1. **codebase://config**: 查看配置信息
2. **codebase://stats**: 查看统计信息

### 提示模板

1. **code_review_prompt**: 生成代码审查提示
2. **search_help_prompt**: 搜索帮助信息

## Pipeline类型说明

| 类型 | 特点 | 适用场景 |
|------|------|----------|
| **simple** | 基础功能，速度快 | 小型项目，快速查询 |
| **pro** | 功能完整，支持图检索 | 中大型项目，全面检索 |
| **balance** | 平衡效果和性能 | 对性能有要求的场景 |
| **best** | 效果最佳，功能最全 | 对检索质量要求最高的场景 |

## 传输方式

### stdio (默认)
适用于Claude Desktop等客户端：
```bash
python mcp_server_fast.py
```

### SSE (Server-Sent Events)
适用于Web应用：
```bash
python start_mcp_server.py --transport sse --port 3000
```

### Streamable HTTP
适用于生产环境：
```bash
python start_mcp_server.py --transport streamable-http --port 3000
```

## 环境变量

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| `REPO_PATH` | 代码库路径 | 必需 |
| `PERSIST_PATH` | 索引存储路径 | `./temp` |
| `PIPELINE_TYPE` | Pipeline类型 | `pro` |
| `DEFAULT_BM25_INDEX_NAME` | BM25索引名称 | `bm25_index_pro` |
| `DEFAULT_EMBEDDING_INDEX_NAME` | Embedding索引名称 | `embedding_index_pro` |
| `DEFAULT_GRAPH_INDEX_NAME` | Graph索引名称 | `graph_index` |

## 故障排除

### 常见问题

1. **"请设置REPO_PATH环境变量"**
   - 确保设置了正确的代码库路径

2. **"索引文件不存在"**
   - 运行索引构建脚本
   - 检查PERSIST_PATH路径

3. **"导入MCP失败"**
   - 安装MCP SDK: `pip install mcp`

4. **"Pipeline初始化失败"**
   - 检查索引文件是否存在
   - 验证代码库路径是否正确

### 调试模式

```bash
python start_mcp_server.py --debug --log-level DEBUG --repo-path /path/to/repo
```

## 开发指南

### 扩展功能

1. 添加新工具：在`mcp_server_fast.py`中使用`@mcp.tool()`装饰器
2. 添加新资源：使用`@mcp.resource()`装饰器
3. 添加新提示：使用`@mcp.prompt()`装饰器

### 自定义Pipeline

继承`BasePipeline`类并实现自定义检索逻辑：

```python
from pipeline_base import BasePipeline

class CustomPipeline(BasePipeline):
    def run(self, query: str, top_k: int = 5):
        # 自定义检索逻辑
        pass
```

## 许可证

MIT License
