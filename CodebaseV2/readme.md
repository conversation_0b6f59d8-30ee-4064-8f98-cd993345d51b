

# 开发迭代计划和版本计划
## base版本

提供最基本的codebase能力，这个最基本的定义，其实是比较模糊的，这里考虑第一个交付版本，或者对接版本。
包含最基本的能力，但是不会特别定制化的能力。期望这个版本可以作为一个基础版本，作为codebase的能力的基础。
之后会不断从pro版本演进为这个bese版本的基线。

### 使用方法
#### 1. 安装依赖
```bash
pip install -r requirements.txt
```
#### 2. 构建数据库索引

你可以选择不同版本的索引版本，主要包括simple，pro，ultimate版本。
```bash
export REPO_PATH="/Users/<USER>/projects/code_qa/test/locAgent/playground/chainlit"
export PERSIST_PATH="/Users/<USER>/projects/code_qa/CodebaseV2/temp/"
bash build_bm25_index_simple.sh
```

你也可以使用混合索引版本，如：
```bash
export REPO_PATH="/Users/<USER>/projects/code_qa/test/locAgent/playground/chainlit"
export PERSIST_PATH="/Users/<USER>/projects/code_qa/CodebaseV2/temp/"
export DEFAULT_BM25_INDEX_NAME="bm25_index_simple"
export DEFAULT_EMBEDDING_INDEX_NAME="embedding_index_simple"
bash scripts/simple/build_index.sh


#### 3. 服务
```
python -m uvicorn main:app --reload
```

## pro版本

提供codebase的能力，包含主流的能力， 如embedding，bm25，rerank等。

## balance版本

根据需要交付的需求，裁剪流程。

## best版本

这里也会有很多效果探索的版本，但是最终会以交付件和git版本来迭代。

提供效果极致的版本，可能会衍生多个版本，如：
- 效果极致的版本，不保证速度
- 效果极致的版本，但是速度更快的版本

