"""
运行本地评测
"""
import json
import time
from typing import List, Dict, Any
from local_pipeline import LocalPipeline
from benchmark.metrics import MetricsCalculator, convert_pipeline_results_to_retrieval_results
from benchmark.dataset import Dataset
from utils import setup_logger

logger = setup_logger(__name__)


def load_jsonl_dataset(jsonl_path: str) -> List[Dict[str, Any]]:
    """加载JSONL格式的数据集"""
    queries = []
    with open(jsonl_path, 'r', encoding='utf-8') as f:
        for line in f:
            if line.strip():
                queries.append(json.loads(line))
    return queries


def run_evaluation():
    """运行评测"""
    print("🚀 开始运行本地评测")
    
    # 1. 初始化Pipeline
    print("📦 初始化Pipeline...")
    pipeline = LocalPipeline()
    
    # 2. 加载数据集
    print("📊 加载评测数据集...")
    queries = load_jsonl_dataset("requests_test_dataset.jsonl")
    print(f"✅ 加载了 {len(queries)} 个查询")
    
    # 3. 初始化指标计算器
    calculator = MetricsCalculator()
    
    # 4. 运行评测
    print("🔍 开始执行评测...")
    results = []
    successful_count = 0
    failed_count = 0
    
    start_time = time.time()
    
    for i, query_data in enumerate(queries):
        query_id = query_data["id"]
        query_text = query_data["query"]
        ground_truth_data = query_data["ground_truth"]
        
        print(f"📝 [{i+1}/{len(queries)}] 评测查询: {query_id} - {query_text}")
        
        try:
            # 执行检索
            pipeline_results = pipeline.run(query_text, top_k=10)
            
            # 转换为评测格式
            retrieval_results = convert_pipeline_results_to_retrieval_results(pipeline_results)
            
            # 转换ground truth格式
            from benchmark.dataset import GroundTruthItem
            ground_truth = [
                GroundTruthItem(
                    file_path=gt["file_path"],
                    start_line=gt.get("start_line"),
                    end_line=gt.get("end_line"),
                    relevance_score=gt["relevance_score"],
                    explanation=gt["explanation"]
                )
                for gt in ground_truth_data
            ]
            
            # 计算指标
            metrics = calculator.calculate_all_metrics(
                retrieval_results, 
                ground_truth, 
                k_values=[1, 3, 5, 10]
            )
            
            # 转换指标格式
            metrics_dict = {name: result.value for name, result in metrics.items()}
            
            result = {
                "query_id": query_id,
                "query_text": query_text,
                "metrics": metrics_dict,
                "retrieved_count": len(retrieval_results),
                "ground_truth_count": len(ground_truth),
                "success": True
            }
            
            results.append(result)
            successful_count += 1
            
            # 显示关键指标
            precision_5 = metrics_dict.get("precision@5", 0)
            recall_5 = metrics_dict.get("recall@5", 0)
            ndcg_5 = metrics_dict.get("ndcg@5", 0)
            print(f"   ✅ P@5: {precision_5:.3f}, R@5: {recall_5:.3f}, NDCG@5: {ndcg_5:.3f}")
            
        except Exception as e:
            logger.error(f"查询 {query_id} 评测失败: {e}")
            result = {
                "query_id": query_id,
                "query_text": query_text,
                "error": str(e),
                "success": False
            }
            results.append(result)
            failed_count += 1
            print(f"   ❌ 失败: {e}")
    
    total_time = time.time() - start_time
    
    # 5. 计算聚合指标
    print("\n📈 计算聚合指标...")
    successful_results = [r for r in results if r["success"]]
    
    if successful_results:
        # 计算平均指标
        metric_names = successful_results[0]["metrics"].keys()
        aggregated_metrics = {}
        
        for metric_name in metric_names:
            values = [r["metrics"][metric_name] for r in successful_results]
            aggregated_metrics[metric_name] = {
                "mean": sum(values) / len(values),
                "min": min(values),
                "max": max(values),
                "count": len(values)
            }
    
    # 6. 生成报告
    print("\n📋 生成评测报告...")
    
    report = {
        "summary": {
            "total_queries": len(queries),
            "successful_queries": successful_count,
            "failed_queries": failed_count,
            "success_rate": successful_count / len(queries) if queries else 0,
            "total_time": total_time,
            "avg_time_per_query": total_time / len(queries) if queries else 0
        },
        "aggregated_metrics": aggregated_metrics if successful_results else {},
        "detailed_results": results,
        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
    }
    
    # 保存报告
    output_file = f"evaluation_report_{int(time.time())}.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    # 7. 显示结果
    print("\n" + "="*60)
    print("🎉 评测完成！")
    print("="*60)
    
    print(f"📊 总体统计:")
    print(f"   总查询数: {report['summary']['total_queries']}")
    print(f"   成功查询: {report['summary']['successful_queries']}")
    print(f"   失败查询: {report['summary']['failed_queries']}")
    print(f"   成功率: {report['summary']['success_rate']:.1%}")
    print(f"   总耗时: {report['summary']['total_time']:.2f}秒")
    print(f"   平均耗时: {report['summary']['avg_time_per_query']:.2f}秒/查询")
    
    if aggregated_metrics:
        print(f"\n📈 关键指标 (平均值):")
        key_metrics = ["precision@1", "precision@3", "precision@5", "recall@5", "ndcg@5", "map", "mrr"]
        for metric in key_metrics:
            if metric in aggregated_metrics:
                value = aggregated_metrics[metric]["mean"]
                print(f"   {metric}: {value:.4f}")
    
    print(f"\n📁 详细报告已保存: {output_file}")
    
    # 8. 分析结果
    if successful_results:
        print(f"\n🔍 结果分析:")
        
        # 按查询类型分析
        type_performance = {}
        for result in successful_results:
            # 从查询ID推断类型
            query_id = result["query_id"]
            if "req_" in query_id:
                query_type = "requests_specific"
            else:
                query_type = "general"
            
            if query_type not in type_performance:
                type_performance[query_type] = []
            type_performance[query_type].append(result["metrics"]["precision@5"])
        
        for qtype, precisions in type_performance.items():
            avg_precision = sum(precisions) / len(precisions)
            print(f"   {qtype}: P@5 = {avg_precision:.3f} ({len(precisions)} 查询)")
        
        # 找出表现最好和最差的查询
        successful_results.sort(key=lambda x: x["metrics"]["precision@5"], reverse=True)
        
        print(f"\n🏆 表现最好的查询:")
        for i, result in enumerate(successful_results[:3]):
            print(f"   {i+1}. {result['query_id']}: {result['query_text'][:50]}... (P@5: {result['metrics']['precision@5']:.3f})")
        
        print(f"\n⚠️  表现最差的查询:")
        for i, result in enumerate(successful_results[-3:]):
            print(f"   {i+1}. {result['query_id']}: {result['query_text'][:50]}... (P@5: {result['metrics']['precision@5']:.3f})")


if __name__ == "__main__":
    run_evaluation()
