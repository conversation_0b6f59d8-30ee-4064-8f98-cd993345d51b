{"id": "req_001", "query": "如何发送HTTP GET请求", "query_type": "api_usage", "difficulty": "easy", "ground_truth": [{"file_path": "src/requests/api.py", "start_line": 1, "end_line": 50, "relevance_score": 1.0, "explanation": "包含get函数的定义和实现"}, {"file_path": "src/requests/__init__.py", "start_line": 1, "end_line": 30, "relevance_score": 0.8, "explanation": "导出get函数的模块入口"}]}
{"id": "req_002", "query": "如何处理HTTP响应", "query_type": "class_search", "difficulty": "medium", "ground_truth": [{"file_path": "src/requests/models.py", "start_line": 1, "end_line": 200, "relevance_score": 1.0, "explanation": "Response类的完整定义"}]}
{"id": "req_003", "query": "Session会话管理的实现", "query_type": "class_search", "difficulty": "hard", "ground_truth": [{"file_path": "src/requests/sessions.py", "start_line": 1, "end_line": 500, "relevance_score": 1.0, "explanation": "Session类的完整实现"}]}
{"id": "req_004", "query": "HTTP适配器的作用", "query_type": "concept_search", "difficulty": "hard", "ground_truth": [{"file_path": "src/requests/adapters.py", "start_line": 1, "end_line": 300, "relevance_score": 1.0, "explanation": "HTTPAdapter类的实现"}]}
{"id": "req_005", "query": "如何处理认证", "query_type": "feature_implementation", "difficulty": "medium", "ground_truth": [{"file_path": "src/requests/auth.py", "start_line": 1, "end_line": 200, "relevance_score": 1.0, "explanation": "认证相关类的实现"}]}
{"id": "req_006", "query": "Cookie处理机制", "query_type": "concept_search", "difficulty": "medium", "ground_truth": [{"file_path": "src/requests/cookies.py", "start_line": 1, "end_line": 400, "relevance_score": 1.0, "explanation": "Cookie处理的完整实现"}]}
{"id": "req_007", "query": "异常处理和错误类型", "query_type": "bug_location", "difficulty": "easy", "ground_truth": [{"file_path": "src/requests/exceptions.py", "start_line": 1, "end_line": 150, "relevance_score": 1.0, "explanation": "所有异常类的定义"}]}
{"id": "req_008", "query": "如何发送POST请求", "query_type": "api_usage", "difficulty": "easy", "ground_truth": [{"file_path": "src/requests/api.py", "start_line": 50, "end_line": 100, "relevance_score": 1.0, "explanation": "post函数的定义"}]}
{"id": "req_009", "query": "工具函数和辅助方法", "query_type": "function_search", "difficulty": "medium", "ground_truth": [{"file_path": "src/requests/utils.py", "start_line": 1, "end_line": 800, "relevance_score": 1.0, "explanation": "各种工具函数的实现"}]}
{"id": "req_010", "query": "HTTP状态码处理", "query_type": "concept_search", "difficulty": "easy", "ground_truth": [{"file_path": "src/requests/status_codes.py", "start_line": 1, "end_line": 100, "relevance_score": 1.0, "explanation": "状态码相关的定义"}]}
{"id": "req_011", "query": "数据结构和容器类", "query_type": "class_search", "difficulty": "medium", "ground_truth": [{"file_path": "src/requests/structures.py", "start_line": 1, "end_line": 200, "relevance_score": 1.0, "explanation": "CaseInsensitiveDict等数据结构"}]}
{"id": "req_012", "query": "钩子函数机制", "query_type": "feature_implementation", "difficulty": "hard", "ground_truth": [{"file_path": "src/requests/hooks.py", "start_line": 1, "end_line": 50, "relevance_score": 1.0, "explanation": "钩子函数的实现"}]}
{"id": "req_013", "query": "兼容性处理代码", "query_type": "concept_search", "difficulty": "medium", "ground_truth": [{"file_path": "src/requests/compat.py", "start_line": 1, "end_line": 100, "relevance_score": 1.0, "explanation": "Python版本兼容性处理"}]}
{"id": "req_014", "query": "SSL证书处理", "query_type": "feature_implementation", "difficulty": "medium", "ground_truth": [{"file_path": "src/requests/certs.py", "start_line": 1, "end_line": 50, "relevance_score": 1.0, "explanation": "SSL证书相关功能"}]}
{"id": "req_015", "query": "如何设置请求头", "query_type": "api_usage", "difficulty": "easy", "ground_truth": [{"file_path": "src/requests/models.py", "start_line": 200, "end_line": 300, "relevance_score": 0.9, "explanation": "Request类中headers的处理"}, {"file_path": "src/requests/sessions.py", "start_line": 300, "end_line": 400, "relevance_score": 0.8, "explanation": "Session中headers的合并逻辑"}]}
{"id": "req_016", "query": "超时设置和处理", "query_type": "feature_implementation", "difficulty": "medium", "ground_truth": [{"file_path": "src/requests/adapters.py", "start_line": 200, "end_line": 300, "relevance_score": 1.0, "explanation": "适配器中的超时处理逻辑"}]}
{"id": "req_017", "query": "代理服务器配置", "query_type": "concept_search", "difficulty": "hard", "ground_truth": [{"file_path": "src/requests/adapters.py", "start_line": 100, "end_line": 200, "relevance_score": 1.0, "explanation": "代理配置相关代码"}, {"file_path": "src/requests/sessions.py", "start_line": 400, "end_line": 500, "relevance_score": 0.7, "explanation": "Session中的代理处理"}]}
{"id": "req_018", "query": "文件上传功能", "query_type": "api_usage", "difficulty": "medium", "ground_truth": [{"file_path": "src/requests/models.py", "start_line": 300, "end_line": 400, "relevance_score": 1.0, "explanation": "Request类中文件处理逻辑"}]}
{"id": "req_019", "query": "重定向处理机制", "query_type": "feature_implementation", "difficulty": "hard", "ground_truth": [{"file_path": "src/requests/sessions.py", "start_line": 100, "end_line": 200, "relevance_score": 1.0, "explanation": "Session中的重定向处理逻辑"}]}
{"id": "req_020", "query": "编码和解码处理", "query_type": "concept_search", "difficulty": "medium", "ground_truth": [{"file_path": "src/requests/utils.py", "start_line": 400, "end_line": 600, "relevance_score": 1.0, "explanation": "编码相关的工具函数"}, {"file_path": "src/requests/models.py", "start_line": 400, "end_line": 500, "relevance_score": 0.8, "explanation": "Response中的编码处理"}]}
