import requests

url = "http://localhost:11434/v1/embeddings"

payload = {
    "model": "text-embedding-qwen3-embedding-0.6b",
    "input": "Silicon flow embedding online: fast, affordable, and high-quality embedding services. come try it out!",
    "encoding_format": "float"
}
headers = {
    "Authorization": "Bearer sk-1234",
    "Content-Type": "application/json"
}

response = requests.request("POST", url, json=payload, headers=headers)

print(response.text)