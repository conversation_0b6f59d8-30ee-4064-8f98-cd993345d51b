"""
结果处理模块
"""
from typing import List, Dict, Any, Optional, Set
from pydantic import BaseModel
import hashlib
from .logger import get_logger

logger = get_logger(__name__)


class ResponseScheme(BaseModel):
    """统一的响应格式"""
    metadata: Dict[str, Any]
    file_path: str
    text: str
    score: float
    source: str = "unknown"  # 标识结果来源：bm25, embedding, graph
    
    def get_content_hash(self) -> str:
        """获取内容哈希，用于去重"""
        content = f"{self.file_path}:{self.text}"
        return hashlib.md5(content.encode()).hexdigest()


class ResultProcessor:
    """结果处理器"""
    
    @staticmethod
    def process_results(results: List[Any], source: str = "unknown") -> List[ResponseScheme]:
        """
        处理检索结果，将其转换为 ResponseScheme 格式
        
        Args:
            results: 原始检索结果
            source: 结果来源标识
            
        Returns:
            处理后的结果列表
        """
        processed_results = []
        for result in results:
            try:
                processed_result = ResponseScheme(
                    metadata=getattr(result, 'metadata', {}),
                    file_path=getattr(result, 'metadata', {}).get('file_path', 'unknown'),
                    text=getattr(result, 'text', ''),
                    score=getattr(result, 'score', 0.0),
                    source=source
                )
                processed_results.append(processed_result)
            except Exception as e:
                logger.warning(f"处理结果时出错: {e}, 跳过此结果")
                continue
        
        return processed_results
    
    @staticmethod
    def deduplicate_results(results: List[ResponseScheme]) -> List[ResponseScheme]:
        """
        去重结果
        
        Args:
            results: 结果列表
            
        Returns:
            去重后的结果列表
        """
        seen_hashes: Set[str] = set()
        deduplicated = []
        
        for result in results:
            content_hash = result.get_content_hash()
            if content_hash not in seen_hashes:
                seen_hashes.add(content_hash)
                deduplicated.append(result)
            else:
                logger.debug(f"发现重复结果，已跳过: {result.file_path}")
        
        logger.info(f"去重前: {len(results)} 个结果，去重后: {len(deduplicated)} 个结果")
        return deduplicated
    
    @staticmethod
    def sort_by_score(results: List[ResponseScheme], reverse: bool = True) -> List[ResponseScheme]:
        """
        按分数排序结果
        
        Args:
            results: 结果列表
            reverse: 是否降序排列
            
        Returns:
            排序后的结果列表
        """
        return sorted(results, key=lambda x: x.score, reverse=reverse)


class ResultMerger:
    """结果合并器"""
    
    def __init__(self, weights: Optional[Dict[str, float]] = None):
        """
        初始化结果合并器
        
        Args:
            weights: 不同来源的权重，例如 {"bm25": 0.4, "embedding": 0.6}
        """
        self.weights = weights or {"bm25": 0.5, "embedding": 0.5, "graph": 0.3}
    
    def merge_results(
        self, 
        bm25_results: List[ResponseScheme] = None,
        embedding_results: List[ResponseScheme] = None,
        graph_results: List[ResponseScheme] = None,
        enable_dedup: bool = True
    ) -> List[ResponseScheme]:
        """
        合并多个来源的结果
        
        Args:
            bm25_results: BM25检索结果
            embedding_results: Embedding检索结果
            graph_results: Graph检索结果
            enable_dedup: 是否启用去重
            
        Returns:
            合并后的结果列表
        """
        all_results = []
        
        # 添加BM25结果
        if bm25_results:
            for result in bm25_results:
                result.source = "bm25"
                result.score *= self.weights.get("bm25", 1.0)
            all_results.extend(bm25_results)
            logger.info(f"添加了 {len(bm25_results)} 个BM25结果")
        
        # 添加Embedding结果
        if embedding_results:
            for result in embedding_results:
                result.source = "embedding"
                result.score *= self.weights.get("embedding", 1.0)
            all_results.extend(embedding_results)
            logger.info(f"添加了 {len(embedding_results)} 个Embedding结果")
        
        # 添加Graph结果
        if graph_results:
            for result in graph_results:
                result.source = "graph"
                result.score *= self.weights.get("graph", 1.0)
            all_results.extend(graph_results)
            logger.info(f"添加了 {len(graph_results)} 个Graph结果")
        
        # 去重
        if enable_dedup:
            all_results = ResultProcessor.deduplicate_results(all_results)
        
        # 按分数排序
        all_results = ResultProcessor.sort_by_score(all_results)
        
        logger.info(f"合并完成，共 {len(all_results)} 个结果")
        return all_results
