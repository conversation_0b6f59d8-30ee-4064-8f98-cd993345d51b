"""
配置管理模块
"""
import os
from typing import Optional, Dict, Any
from pydantic import BaseModel, Field


class RetrievalConfig(BaseModel):
    """检索配置"""
    bm25_top_k: int = Field(default=10, description="BM25检索返回的结果数量")
    embedding_top_k: int = Field(default=10, description="Embedding检索返回的结果数量")
    graph_top_k: int = Field(default=5, description="Graph检索返回的结果数量")
    final_top_k: int = Field(default=5, description="最终返回的结果数量")
    enable_rerank: bool = Field(default=True, description="是否启用重排序")
    rerank_threshold: float = Field(default=0.1, description="重排序阈值")
    rerank_top_n: int = Field(default=10, description="重排序返回的结果数量")


class ChunkConfig(BaseModel):
    """文档切片配置"""
    chunk_lines: int = Field(default=100, description="每个切片的行数")
    chunk_lines_overlap: int = Field(default=15, description="切片重叠行数")
    max_chars: int = Field(default=1500, description="每个切片的最大字符数")


class IndexConfig(BaseModel):
    """索引配置"""
    bm25_index_name: str = Field(default="bm25_index", description="BM25索引名称")
    embedding_index_name: str = Field(default="embedding_index", description="Embedding索引名称")
    graph_index_name: str = Field(default="graph_index", description="Graph索引名称")


class Config:
    """全局配置管理"""
    
    def __init__(self):
        self.repo_path = os.getenv("REPO_PATH", "")
        self.persist_path = os.getenv("PERSIST_PATH", "./temp")
        self.bm25_index_name = os.getenv("DEFAULT_BM25_INDEX_NAME", "bm25_index")
        self.embedding_index_name = os.getenv("DEFAULT_EMBEDDING_INDEX_NAME", "embedding_index")
        self.graph_index_name = os.getenv("DEFAULT_GRAPH_INDEX_NAME", "graph_index")
        
        # 配置对象
        self.retrieval = RetrievalConfig()
        self.chunk = ChunkConfig()
        self.index = IndexConfig(
            bm25_index_name=self.bm25_index_name,
            embedding_index_name=self.embedding_index_name,
            graph_index_name=self.graph_index_name
        )
    
    def get_index_paths(self, repo_name: str) -> Dict[str, str]:
        """获取索引路径"""
        return {
            "bm25": os.path.join(self.persist_path, self.bm25_index_name, repo_name),
            "embedding": os.path.join(self.persist_path, self.embedding_index_name, repo_name),
            "graph": os.path.join(self.persist_path, self.graph_index_name, f"{repo_name}.pkl")
        }
    
    def validate(self) -> bool:
        """验证配置"""
        if not self.repo_path:
            raise ValueError("REPO_PATH environment variable is required")
        if not os.path.exists(self.repo_path):
            raise ValueError(f"Repository path does not exist: {self.repo_path}")
        return True


# 全局配置实例
config = Config()
