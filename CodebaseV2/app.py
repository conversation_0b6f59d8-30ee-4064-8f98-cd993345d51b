import os
import json
from typing import Optional

from litellm import completion

# from flask import Flask, jsonify, request, Response
from fastapi import FastAP<PERSON>, Request
from fastapi.responses import JSONResponse
from fastapi.logger import logger
import uvicorn
from dotenv import load_dotenv
from httpx import request

# 支持多种pipeline
from pipeline_simple import Pipeline as SimplePipeline
from pipeline_pro import Pipeline as ProPipeline
from pipeline_balance import Pipeline as BalancePipeline
from pipeline_best import Pipeline as BestPipeline
from utils import Config, setup_logger

load_dotenv()

# 设置日志
app_logger = setup_logger("codebase_app")

app = FastAPI(title="Codebase QA API", version="0.2.0")

# 配置管理
config = Config()
try:
    config.validate()
except ValueError as e:
    app_logger.error(f"配置验证失败: {e}")
    raise

INTFACE_VERSION = "0.2.0"

# 获取索引路径
repo_name = os.path.basename(config.repo_path)
index_paths = config.get_index_paths(repo_name)

# 选择pipeline类型（可以通过环境变量配置）
PIPELINE_TYPE = os.environ.get("PIPELINE_TYPE", "pro").lower()

pipeline_classes = {
    "simple": SimplePipeline,
    "pro": ProPipeline,
    "balance": BalancePipeline,
    "best": BestPipeline
}

if PIPELINE_TYPE not in pipeline_classes:
    app_logger.warning(f"未知的pipeline类型: {PIPELINE_TYPE}，使用默认的pro类型")
    PIPELINE_TYPE = "pro"

PipelineClass = pipeline_classes[PIPELINE_TYPE]
app_logger.info(f"使用 {PIPELINE_TYPE} pipeline")

# 初始化pipeline
try:
    if PIPELINE_TYPE == "simple":
        pipeline = PipelineClass(
            bm25_persist_path=index_paths["bm25"],
            emb_persist_path=index_paths["embedding"]
        )
    else:
        pipeline = PipelineClass(
            bm25_persist_path=index_paths["bm25"],
            emb_persist_path=index_paths["embedding"],
            graph_persist_path=index_paths["graph"]
        )
    app_logger.info("Pipeline初始化成功")
except Exception as e:
    app_logger.error(f"Pipeline初始化失败: {e}")
    raise


def generate_context(query: str, top_k: int = 5):
    """生成查询上下文"""
    try:
        results = pipeline.run(query, top_k=top_k)
        app_logger.debug(f"查询 '{query}' 返回 {len(results)} 条结果")
        return {
            "query": query,
            "results": results,
            "pipeline_type": PIPELINE_TYPE,
            "total_results": len(results)
        }
    except Exception as e:
        app_logger.error(f"生成上下文失败: {e}")
        return {
            "query": query,
            "results": [],
            "error": str(e)
        }


def _search_entity(query: str, top_n=None):
    """根据query的实体名搜索相关的实体和其相关的实体"""
    entity_searcher = pipeline.graph_retriever.entity_searcher
    dependency_searcher = pipeline.graph_retriever.dependency_searcher
    # print(pipeline.graph_retriever.repo_tree)
    if not entity_searcher.has_node(query):
        return {"error": "entity not found"}
    neighbors = dependency_searcher.get_neighbors(query)[0]
    if isinstance(top_n, int):
        neighbors = neighbors[:top_n]
    neighbors.append(query)
    entities = entity_searcher.get_node_data(
        neighbors, return_code_content=True, wrap_with_ln=False
    )
    return {"entities": entities}


def paser_query_for_entity(query: str):
    """
    从query中解析出实体
    """

    os.environ["LM_STUDIO_API_BASE"] = "http://127.0.0.1:1234/v1"
    os.environ["LM_STUDIO_API_KEY"] = "lm_studio"
    response = completion(
        model="lm_studio/qwen2.5-coder-7b-instruct-mlx",
        messages=[
            {
                "role": "user",
                "content": query,
            }
        ],
    )
    return response.choices[0].message.content


@app.post("/query")
async def get_query_context(request: Request):
    request_data = await request.json()
    query = request_data["query"]

    if "@codebase" in query:
        query = query.replace("@codebase", "").strip()
        context: dict = generate_context(query)
        logger.debug("Generated context for query with @codebase.")
        return JSONResponse(content=context)
    else:
        return JSONResponse(
            content={
                "error": "Invalid query format, use @codebase to start query"
            },
            status_code=400,
        )


@app.post("/search_entity")  # FastAPI使用post装饰器而不是route
async def search_entity(request: Request):
    # 从请求体中获取JSON数据
    request_data = await request.json()
    query = request_data.get("query")

    if not query:
        return JSONResponse(
            content={"error": "Query parameter is required"}, status_code=400
        )

    # todo paser entity
    # res = paser_query_for_entity(query)
    # print(res)

    top_n = request_data.get("top_n", None)
    entities = _search_entity(query, top_n)
    if "error" in entities:
        logger.debug(entities)
        entities = []

    data = {
        "interfaceVersion": INTFACE_VERSION,
        "query": {
            "text": query,
            "entity": "",
            "description": "",
            "requirement": "",
        },
        "entities": entities,
    }
    return JSONResponse(content=data)


@app.get("/runtime/v1")
def get_runtime_info():
    """获取运行时信息"""
    try:
        repo_path = config.repo_path
        sdk_version = "build-profile.json5"
        sdk_version_path = os.path.join(repo_path, sdk_version)

        if os.path.exists(sdk_version_path):
            with open(sdk_version_path, "r") as f:
                sdk_version_data = json.load(f)
            return {"build-profile.json5": sdk_version_data}
        else:
            return {
                "error": "build-profile.json5 not found",
                "repo_path": repo_path,
                "pipeline_type": PIPELINE_TYPE
            }
    except Exception as e:
        app_logger.error(f"获取运行时信息失败: {e}")
        return {"error": str(e)}


@app.get("/health")
def health_check():
    """健康检查接口"""
    return {
        "status": "healthy",
        "version": INTFACE_VERSION,
        "pipeline_type": PIPELINE_TYPE,
        "repo_path": config.repo_path
    }


if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=5001)
