import os
import json

from litellm import completion

# from flask import Flask, jsonify, request, Response
from fastapi import Fast<PERSON><PERSON>, Request
from fastapi.responses import JSONResponse
from fastapi.logger import logger
import uvicorn
from dotenv import load_dotenv
from httpx import request

# from pipeline_simple import Pipeline
from pipeline_pro import Pipeline

load_dotenv()
# app = Flask(__name__)
app = FastAPI()
REPO_PATH = os.environ.get("REPO_PATH")
PERSIST_PATH = os.environ.get("PERSIST_PATH")
DEFAULT_BM25_INDEX_NAME = os.environ.get("DEFAULT_BM25_INDEX_NAME")
DEFAULT_EMBEDDING_INDEX_NAME = os.environ.get("DEFAULT_EMBEDDING_INDEX_NAME")
DEFAULT_GRAPH_INDEX_NAME = os.environ.get("DEFAULT_GRAPH_INDEX_NAME")
INTFACE_VERSION = "0.0.1"


repo_name = os.path.basename(REPO_PATH)
bm25_persist_path = os.path.join(
    PERSIST_PATH, DEFAULT_BM25_INDEX_NAME, repo_name
)
ebm_persist_path = os.path.join(
    PERSIST_PATH, DEFAULT_EMBEDDING_INDEX_NAME, repo_name
)
graph_persist_path = os.path.join(
    PERSIST_PATH, DEFAULT_GRAPH_INDEX_NAME, f"{repo_name}.pkl"
)
pipeline = Pipeline(
    graph_persist_path=graph_persist_path,
    emb_persist_path=ebm_persist_path,
    bm25_persist_path=bm25_persist_path,
)


def generate_context(query: str):
    results = pipeline.run(query)
    logger.debug(results)
    return {
        "query": query,
        "results": results,
    }


def _search_entity(query: str, top_n=None):
    """根据query的实体名搜索相关的实体和其相关的实体"""
    entity_searcher = pipeline.graph_retriever.entity_searcher
    dependency_searcher = pipeline.graph_retriever.dependency_searcher
    # print(pipeline.graph_retriever.repo_tree)
    if not entity_searcher.has_node(query):
        return {"error": "entity not found"}
    neighbors = dependency_searcher.get_neighbors(query)[0]
    if isinstance(top_n, int):
        neighbors = neighbors[:top_n]
    neighbors.append(query)
    entities = entity_searcher.get_node_data(
        neighbors, return_code_content=True, wrap_with_ln=False
    )
    return {"entities": entities}


def paser_query_for_entity(query: str):
    """
    从query中解析出实体
    """

    os.environ["LM_STUDIO_API_BASE"] = "http://127.0.0.1:1234/v1"
    os.environ["LM_STUDIO_API_KEY"] = "lm_studio"
    response = completion(
        model="lm_studio/qwen2.5-coder-7b-instruct-mlx",
        messages=[
            {
                "role": "user",
                "content": query,
            }
        ],
    )
    return response.choices[0].message.content


@app.post("/query")
async def get_query_context(request: Request):
    request_data = await request.json()
    query = request_data["query"]

    if "@codebase" in query:
        query = query.replace("@codebase", "").strip()
        context: dict = generate_context(query)
        logger.debug("Generated context for query with @codebase.")
        return JSONResponse(content=context)
    else:
        return JSONResponse(
            content={
                "error": "Invalid query format, use @codebase to start query"
            },
            status_code=400,
        )


@app.post("/search_entity")  # FastAPI使用post装饰器而不是route
async def search_entity(request: Request):
    # 从请求体中获取JSON数据
    request_data = await request.json()
    query = request_data.get("query")

    if not query:
        return JSONResponse(
            content={"error": "Query parameter is required"}, status_code=400
        )

    # todo paser entity
    # res = paser_query_for_entity(query)
    # print(res)

    top_n = request_data.get("top_n", None)
    entities = _search_entity(query, top_n)
    if "error" in entities:
        logger.debug(entities)
        entities = []

    data = {
        "interfaceVersion": INTFACE_VERSION,
        "query": {
            "text": query,
            "entity": "",
            "description": "",
            "requirement": "",
        },
        "entities": entities,
    }
    return JSONResponse(content=data)


@app.get("/runtime/v1")
def get_runtime_info():
    repo_path = REPO_PATH
    sdk_version = "build-profile.json5"
    sdk_version_path = os.path.join(repo_path, sdk_version)
    with open(sdk_version_path, "r") as f:
        sdk_version = json.load(f)
    return {"build-profile.json5": sdk_version}


if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=5001)
