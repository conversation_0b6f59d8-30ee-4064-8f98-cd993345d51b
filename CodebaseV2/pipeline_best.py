"""
Best Pipeline - 效果极致版本
提供最佳效果的版本，可能会牺牲一些速度
"""
from typing import List, Dict, Any, Optional
from retriever import BM25RetrieverPro, EmbeddingRetrieverPro, GraphRetriever
from pipeline_base import BasePipeline
from utils import setup_logger, Config, ResponseScheme

logger = setup_logger(__name__)


class AdvancedQueryAugment:
    """高级查询增强器"""

    def augment(self, query: str) -> str:
        """
        高级查询增强逻辑

        Args:
            query: 原始查询

        Returns:
            增强后的查询
        """
        # 可以在这里实现更复杂的查询增强逻辑
        # 例如：同义词扩展、查询重写、多语言支持等

        # 基础实现：添加一些常见的代码相关关键词
        code_keywords = ["function", "class", "method", "implementation", "code"]

        # 如果查询中没有明确的代码关键词，可以添加一些
        query_lower = query.lower()
        has_code_keyword = any(keyword in query_lower for keyword in code_keywords)

        if not has_code_keyword and len(query.split()) < 5:
            # 对于短查询，添加代码相关上下文
            augmented = f"{query} implementation code"
            logger.debug(f"查询增强: '{query}' -> '{augmented}'")
            return augmented

        return query


class Pipeline(BasePipeline):
    """Best Pipeline实现 - 追求最佳效果"""

    def __init__(
        self,
        bm25_persist_path: str = None,
        emb_persist_path: str = None,
        graph_persist_path: str = None,
        config: Optional[Config] = None
    ) -> None:
        # 自定义配置以获得最佳效果
        if config is None:
            config = Config()
            # 调整配置以获得最佳效果
            config.retrieval.bm25_top_k = 15
            config.retrieval.embedding_top_k = 15
            config.retrieval.graph_top_k = 10
            config.retrieval.enable_rerank = True
            config.retrieval.rerank_top_n = 15
            config.retrieval.rerank_threshold = 0.05  # 更低的阈值以获得更多结果

        super().__init__(
            bm25_persist_path=bm25_persist_path,
            emb_persist_path=emb_persist_path,
            graph_persist_path=graph_persist_path,
            config=config
        )

        # 使用高级查询增强器
        self.query_augment = AdvancedQueryAugment()

        # 调整结果合并器权重以获得最佳效果
        self.result_merger.weights = {
            "bm25": 0.3,
            "embedding": 0.5,
            "graph": 0.4
        }

    def _create_bm25_retriever(self, persist_path: str):
        """创建BM25检索器 - 使用Pro版本"""
        return BM25RetrieverPro(persist_path)

    def _create_embedding_retriever(self, persist_path: str):
        """创建Embedding检索器 - 使用Pro版本"""
        return EmbeddingRetrieverPro(persist_path)

    def _advanced_result_fusion(self, results: List[ResponseScheme]) -> List[ResponseScheme]:
        """
        高级结果融合逻辑

        Args:
            results: 原始结果列表

        Returns:
            融合后的结果列表
        """
        # 可以在这里实现更复杂的结果融合逻辑
        # 例如：基于文件类型的权重调整、基于代码复杂度的排序等

        # 基础实现：根据文件类型调整权重
        for result in results:
            file_path = result.file_path.lower()

            # 提高核心代码文件的权重
            if any(pattern in file_path for pattern in ['.py', '.js', '.java', '.cpp']):
                result.score *= 1.2

            # 降低测试文件的权重
            if any(pattern in file_path for pattern in ['test', 'spec', '__test__']):
                result.score *= 0.8

            # 提高主要模块文件的权重
            if any(pattern in file_path for pattern in ['main', 'index', 'core', 'base']):
                result.score *= 1.1

        return sorted(results, key=lambda x: x.score, reverse=True)

    def run(self, query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """
        运行Best Pipeline

        Args:
            query: 查询字符串
            top_k: 返回结果数量

        Returns:
            检索结果列表
        """
        logger.info(f"开始执行Best Pipeline查询: '{query}'")

        # Step 1: 高级查询增强
        augmented_query = self.query_augment.augment(query)
        logger.debug(f"高级查询增强: '{query}' -> '{augmented_query}'")

        # Step 2: 多源检索
        bm25_results = self._retrieve_bm25(augmented_query)
        embedding_results = self._retrieve_embedding(augmented_query)
        graph_results = self._retrieve_graph(augmented_query)

        # Step 3: 合并结果
        merged_results = self.result_merger.merge_results(
            bm25_results=bm25_results,
            embedding_results=embedding_results,
            graph_results=graph_results
        )

        # Step 4: 高级结果融合
        fused_results = self._advanced_result_fusion(merged_results)

        # Step 5: 重排序
        reranked_results = self._rerank_results(augmented_query, fused_results)

        # Step 6: 返回结果
        final_results = reranked_results[:top_k]
        result_dicts = [result.model_dump() for result in final_results]

        logger.info(f"Best Pipeline完成，返回 {len(result_dicts)} 条结果")
        return result_dicts


if __name__ == "__main__":
    # 测试代码
    import os

    repo_name = "test_repo"
    persist_path = "./temp"

    bm25_persist_path = os.path.join(persist_path, "bm25_index_pro", repo_name)
    emb_persist_path = os.path.join(persist_path, "embedding_index_pro", repo_name)
    graph_persist_path = os.path.join(persist_path, "graph_index", f"{repo_name}.pkl")

    # 创建pipeline实例
    pipeline = Pipeline(
        bm25_persist_path=bm25_persist_path,
        emb_persist_path=emb_persist_path,
        graph_persist_path=graph_persist_path
    )

    # 执行查询
    query = "代码检索"
    results = pipeline.run(query, top_k=5)

    from pprint import pprint
    pprint(results)