"""
基础Pipeline类
"""
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
import os

from retriever import BM25RetrieverSimple, BM25RetrieverPro
from retriever import EmbeddingRetrieverSimple, EmbeddingRetrieverPro
from retriever import GraphRetriever
from rerank.dashscope_rerank import rerank
from utils import Config, setup_logger, ResultProcessor, ResultMerger, ResponseScheme

logger = setup_logger(__name__)


class QueryAugment:
    """查询增强器"""
    
    def augment(self, query: str) -> str:
        """
        增强查询
        
        Args:
            query: 原始查询
            
        Returns:
            增强后的查询
        """
        # 基础实现，直接返回原查询
        # 可以在子类中重写以实现更复杂的查询增强逻辑
        return query


class BasePipeline(ABC):
    """基础Pipeline类"""
    
    def __init__(
        self,
        bm25_persist_path: Optional[str] = None,
        emb_persist_path: Optional[str] = None,
        graph_persist_path: Optional[str] = None,
        config: Optional[Config] = None
    ):
        """
        初始化Pipeline
        
        Args:
            bm25_persist_path: BM25索引路径
            emb_persist_path: Embedding索引路径
            graph_persist_path: Graph索引路径
            config: 配置对象
        """
        self.config = config or Config()
        
        # 索引路径
        self.bm25_persist_path = bm25_persist_path
        self.emb_persist_path = emb_persist_path
        self.graph_persist_path = graph_persist_path
        
        # 检索器
        self.bm25_retriever = None
        self.emb_retriever = None
        self.graph_retriever = None
        
        # 组件
        self.query_augment = QueryAugment()
        self.result_processor = ResultProcessor()
        self.result_merger = ResultMerger()
        
        # 初始化检索器
        self._init_retrievers()
    
    def _init_retrievers(self):
        """初始化检索器"""
        # 初始化BM25检索器
        if self.bm25_persist_path and os.path.exists(self.bm25_persist_path):
            try:
                self.bm25_retriever = self._create_bm25_retriever(self.bm25_persist_path)
                logger.info(f"成功加载BM25索引: {self.bm25_persist_path}")
            except Exception as e:
                logger.error(f"加载BM25索引失败: {e}")
        else:
            logger.warning("未提供BM25索引路径或路径不存在")
        
        # 初始化Embedding检索器
        if self.emb_persist_path and os.path.exists(self.emb_persist_path):
            try:
                self.emb_retriever = self._create_embedding_retriever(self.emb_persist_path)
                logger.info(f"成功加载Embedding索引: {self.emb_persist_path}")
            except Exception as e:
                logger.error(f"加载Embedding索引失败: {e}")
        else:
            logger.warning("未提供Embedding索引路径或路径不存在")
        
        # 初始化Graph检索器
        if self.graph_persist_path and os.path.exists(self.graph_persist_path):
            try:
                self.graph_retriever = GraphRetriever(self.graph_persist_path)
                logger.info(f"成功加载Graph索引: {self.graph_persist_path}")
            except Exception as e:
                logger.error(f"加载Graph索引失败: {e}")
        else:
            logger.warning("未提供Graph索引路径或路径不存在")
    
    @abstractmethod
    def _create_bm25_retriever(self, persist_path: str):
        """创建BM25检索器，子类需要实现"""
        pass
    
    @abstractmethod
    def _create_embedding_retriever(self, persist_path: str):
        """创建Embedding检索器，子类需要实现"""
        pass
    
    def _retrieve_bm25(self, query: str) -> List[ResponseScheme]:
        """BM25检索"""
        if not self.bm25_retriever:
            return []
        
        try:
            results = self.bm25_retriever.retrieve(query, self.config.retrieval.bm25_top_k)
            processed_results = self.result_processor.process_results(results, "bm25")
            logger.info(f"BM25检索到 {len(processed_results)} 条结果")
            return processed_results
        except Exception as e:
            logger.error(f"BM25检索失败: {e}")
            return []
    
    def _retrieve_embedding(self, query: str) -> List[ResponseScheme]:
        """Embedding检索"""
        if not self.emb_retriever:
            return []
        
        try:
            results = self.emb_retriever.retrieve(query, self.config.retrieval.embedding_top_k)
            processed_results = self.result_processor.process_results(results, "embedding")
            logger.info(f"Embedding检索到 {len(processed_results)} 条结果")
            return processed_results
        except Exception as e:
            logger.error(f"Embedding检索失败: {e}")
            return []
    
    def _retrieve_graph(self, query: str) -> List[ResponseScheme]:
        """Graph检索"""
        if not self.graph_retriever:
            return []
        
        try:
            # Graph检索器的retrieve方法需要实现
            # 这里先返回空列表
            logger.info("Graph检索暂未实现")
            return []
        except Exception as e:
            logger.error(f"Graph检索失败: {e}")
            return []
    
    def _rerank_results(self, query: str, results: List[ResponseScheme]) -> List[ResponseScheme]:
        """重排序结果"""
        if not self.config.retrieval.enable_rerank or not results:
            return results
        
        try:
            documents = [result.text for result in results]
            rerank_results = rerank(
                query, 
                documents,
                top_n=self.config.retrieval.rerank_top_n,
                threshold=self.config.retrieval.rerank_threshold
            )
            
            if rerank_results:
                reranked = [results[index] for index, _ in rerank_results]
                logger.info(f"重排序完成，返回 {len(reranked)} 条结果")
                return reranked
            else:
                logger.warning("重排序未返回结果，使用原始结果")
                return results
        except Exception as e:
            logger.error(f"重排序失败: {e}，使用原始结果")
            return results
    
    @staticmethod
    def show_results(results: List[ResponseScheme]):
        """显示结果（用于调试）"""
        for i, result in enumerate(results):
            logger.debug(f"结果 {i+1}: {result.file_path} (score: {result.score}, source: {result.source})")
    
    @abstractmethod
    def run(self, query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """
        运行Pipeline，子类需要实现具体逻辑
        
        Args:
            query: 查询字符串
            top_k: 返回结果数量
            
        Returns:
            检索结果列表
        """
        pass
