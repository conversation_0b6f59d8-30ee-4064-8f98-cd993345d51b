"""
MCP Server for Codebase QA
实现符合MCP规范的代码库检索服务器
"""
import os
import json
from typing import List, Dict, Any, Optional
from contextlib import asynccontextmanager
from collections.abc import AsyncIterator

from mcp.server import Server
from mcp.server.models import InitializationOptions
from mcp.server.lowlevel import NotificationOptions
import mcp.server.stdio
import mcp.types as types

# 导入我们的pipeline
from pipeline_simple import Pipeline as SimplePipeline
from pipeline_pro import Pipeline as ProPipeline
from pipeline_balance import Pipeline as BalancePipeline
from pipeline_best import Pipeline as BestPipeline
from utils import Config, setup_logger

logger = setup_logger(__name__)


class CodebaseContext:
    """代码库上下文管理"""
    def __init__(self):
        self.config = Config()
        self.pipeline = None
        self.pipeline_type = os.environ.get("PIPELINE_TYPE", "pro").lower()
        
    async def initialize(self):
        """初始化代码库检索pipeline"""
        try:
            self.config.validate()
            
            # 获取索引路径
            repo_name = os.path.basename(self.config.repo_path)
            index_paths = self.config.get_index_paths(repo_name)
            
            # 选择pipeline类型
            pipeline_classes = {
                "simple": SimplePipeline,
                "pro": ProPipeline,
                "balance": BalancePipeline,
                "best": BestPipeline
            }
            
            if self.pipeline_type not in pipeline_classes:
                logger.warning(f"未知的pipeline类型: {self.pipeline_type}，使用默认的pro类型")
                self.pipeline_type = "pro"
            
            PipelineClass = pipeline_classes[self.pipeline_type]
            logger.info(f"使用 {self.pipeline_type} pipeline")
            
            # 初始化pipeline
            if self.pipeline_type == "simple":
                self.pipeline = PipelineClass(
                    bm25_persist_path=index_paths["bm25"],
                    emb_persist_path=index_paths["embedding"]
                )
            else:
                self.pipeline = PipelineClass(
                    bm25_persist_path=index_paths["bm25"],
                    emb_persist_path=index_paths["embedding"],
                    graph_persist_path=index_paths["graph"]
                )
            
            logger.info("Codebase pipeline初始化成功")
            
        except Exception as e:
            logger.error(f"初始化失败: {e}")
            raise
    
    async def cleanup(self):
        """清理资源"""
        logger.info("清理codebase资源")


@asynccontextmanager
async def server_lifespan(server: Server) -> AsyncIterator[Dict[str, Any]]:
    """管理服务器生命周期"""
    # 启动时初始化
    codebase_ctx = CodebaseContext()
    await codebase_ctx.initialize()
    
    try:
        yield {"codebase": codebase_ctx}
    finally:
        # 关闭时清理
        await codebase_ctx.cleanup()


# 创建MCP服务器
server = Server("codebase-qa", lifespan=server_lifespan)


@server.list_resources()
async def handle_list_resources() -> List[types.Resource]:
    """列出可用的资源"""
    return [
        types.Resource(
            uri="codebase://config",
            name="配置信息",
            description="当前代码库的配置信息",
            mimeType="application/json"
        ),
        types.Resource(
            uri="codebase://stats",
            name="统计信息", 
            description="代码库的统计信息",
            mimeType="application/json"
        )
    ]


@server.read_resource()
async def handle_read_resource(uri: str) -> str:
    """读取资源内容"""
    ctx = server.request_context
    codebase_ctx = ctx.lifespan_context["codebase"]
    
    if uri == "codebase://config":
        config_info = {
            "repo_path": codebase_ctx.config.repo_path,
            "pipeline_type": codebase_ctx.pipeline_type,
            "retrieval_config": codebase_ctx.config.retrieval.model_dump(),
            "chunk_config": codebase_ctx.config.chunk.model_dump()
        }
        return json.dumps(config_info, indent=2, ensure_ascii=False)
    
    elif uri == "codebase://stats":
        stats_info = {
            "pipeline_type": codebase_ctx.pipeline_type,
            "repo_name": os.path.basename(codebase_ctx.config.repo_path),
            "available_retrievers": []
        }
        
        # 检查可用的检索器
        if hasattr(codebase_ctx.pipeline, 'bm25_retriever') and codebase_ctx.pipeline.bm25_retriever:
            stats_info["available_retrievers"].append("bm25")
        if hasattr(codebase_ctx.pipeline, 'emb_retriever') and codebase_ctx.pipeline.emb_retriever:
            stats_info["available_retrievers"].append("embedding")
        if hasattr(codebase_ctx.pipeline, 'graph_retriever') and codebase_ctx.pipeline.graph_retriever:
            stats_info["available_retrievers"].append("graph")
            
        return json.dumps(stats_info, indent=2, ensure_ascii=False)
    
    else:
        raise ValueError(f"未知的资源URI: {uri}")


@server.list_tools()
async def handle_list_tools() -> List[types.Tool]:
    """列出可用的工具"""
    return [
        types.Tool(
            name="search_codebase",
            description="在代码库中搜索相关的代码片段",
            inputSchema={
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "搜索查询，使用自然语言描述你要找的代码功能"
                    },
                    "top_k": {
                        "type": "integer",
                        "description": "返回的结果数量，默认为5",
                        "default": 5,
                        "minimum": 1,
                        "maximum": 20
                    }
                },
                "required": ["query"]
            }
        ),
        types.Tool(
            name="get_file_content",
            description="获取指定文件的完整内容",
            inputSchema={
                "type": "object",
                "properties": {
                    "file_path": {
                        "type": "string",
                        "description": "相对于代码库根目录的文件路径"
                    }
                },
                "required": ["file_path"]
            }
        )
    ]


@server.call_tool()
async def handle_call_tool(name: str, arguments: Dict[str, Any]) -> List[types.TextContent]:
    """处理工具调用"""
    ctx = server.request_context
    codebase_ctx = ctx.lifespan_context["codebase"]
    
    if name == "search_codebase":
        query = arguments["query"]
        top_k = arguments.get("top_k", 5)
        
        try:
            # 执行搜索
            results = codebase_ctx.pipeline.run(query, top_k=top_k)
            
            # 格式化结果
            if not results:
                return [types.TextContent(
                    type="text",
                    text=f"未找到与查询 '{query}' 相关的代码片段。"
                )]
            
            formatted_results = []
            formatted_results.append(f"找到 {len(results)} 个相关的代码片段：\n")
            
            for i, result in enumerate(results, 1):
                file_path = result.get("file_path", "未知文件")
                text = result.get("text", "")
                score = result.get("score", 0)
                source = result.get("source", "unknown")
                
                formatted_results.append(f"## 结果 {i} (来源: {source}, 相关度: {score:.3f})")
                formatted_results.append(f"**文件**: {file_path}")
                formatted_results.append(f"**内容**:")
                formatted_results.append(f"```")
                formatted_results.append(text)
                formatted_results.append(f"```\n")
            
            return [types.TextContent(
                type="text",
                text="\n".join(formatted_results)
            )]
            
        except Exception as e:
            logger.error(f"搜索失败: {e}")
            return [types.TextContent(
                type="text",
                text=f"搜索失败: {str(e)}"
            )]
    
    elif name == "get_file_content":
        file_path = arguments["file_path"]
        
        try:
            # 构建完整路径
            full_path = os.path.join(codebase_ctx.config.repo_path, file_path)
            
            # 安全检查：确保文件在代码库目录内
            if not os.path.abspath(full_path).startswith(os.path.abspath(codebase_ctx.config.repo_path)):
                return [types.TextContent(
                    type="text",
                    text="错误：文件路径超出代码库范围"
                )]
            
            if not os.path.exists(full_path):
                return [types.TextContent(
                    type="text",
                    text=f"错误：文件 {file_path} 不存在"
                )]
            
            # 读取文件内容
            with open(full_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            return [types.TextContent(
                type="text",
                text=f"文件: {file_path}\n\n```\n{content}\n```"
            )]
            
        except Exception as e:
            logger.error(f"读取文件失败: {e}")
            return [types.TextContent(
                type="text",
                text=f"读取文件失败: {str(e)}"
            )]
    
    else:
        raise ValueError(f"未知的工具: {name}")


async def main():
    """主函数"""
    # 使用stdio传输
    async with mcp.server.stdio.stdio_server() as (read_stream, write_stream):
        await server.run(
            read_stream,
            write_stream,
            InitializationOptions(
                server_name="codebase-qa",
                server_version="0.2.0",
                capabilities=server.get_capabilities(
                    notification_options=NotificationOptions(),
                    experimental_capabilities={},
                ),
            ),
        )


if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
