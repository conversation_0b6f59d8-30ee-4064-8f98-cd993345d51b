#!/usr/bin/env python3
"""
启动MCP服务器的脚本
支持不同的传输方式和配置选项
"""
import os
import sys
import argparse
import asyncio
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from utils import setup_logger

logger = setup_logger(__name__)


def setup_environment(args):
    """设置环境变量"""
    if args.repo_path:
        os.environ["REPO_PATH"] = args.repo_path
    if args.persist_path:
        os.environ["PERSIST_PATH"] = args.persist_path
    if args.pipeline_type:
        os.environ["PIPELINE_TYPE"] = args.pipeline_type
    
    # 设置默认索引名称
    os.environ.setdefault("DEFAULT_BM25_INDEX_NAME", "bm25_index_pro")
    os.environ.setdefault("DEFAULT_EMBEDDING_INDEX_NAME", "embedding_index_pro")
    os.environ.setdefault("DEFAULT_GRAPH_INDEX_NAME", "graph_index")
    
    # 验证必需的环境变量
    if not os.environ.get("REPO_PATH"):
        logger.error("请设置REPO_PATH环境变量或使用--repo-path参数")
        sys.exit(1)
    
    repo_path = os.environ["REPO_PATH"]
    if not os.path.exists(repo_path):
        logger.error(f"代码库路径不存在: {repo_path}")
        sys.exit(1)
    
    logger.info(f"代码库路径: {repo_path}")
    logger.info(f"Pipeline类型: {os.environ.get('PIPELINE_TYPE', 'pro')}")


def run_stdio_server():
    """运行stdio传输的MCP服务器"""
    logger.info("启动stdio MCP服务器")
    
    try:
        from mcp_server import main
        asyncio.run(main())
    except ImportError as e:
        logger.error(f"导入MCP服务器失败: {e}")
        logger.error("请确保已安装MCP Python SDK: pip install mcp")
        sys.exit(1)
    except Exception as e:
        logger.error(f"服务器运行失败: {e}")
        sys.exit(1)


def run_fastmcp_server(transport="stdio", host="localhost", port=3000):
    """运行FastMCP服务器"""
    logger.info(f"启动FastMCP服务器 (传输: {transport})")
    
    try:
        from mcp_server_fast import mcp
        
        if transport == "stdio":
            mcp.run()
        elif transport == "sse":
            mcp.run(transport="sse", host=host, port=port)
        elif transport == "streamable-http":
            mcp.run(transport="streamable-http", host=host, port=port)
        else:
            logger.error(f"不支持的传输方式: {transport}")
            sys.exit(1)
            
    except ImportError as e:
        logger.error(f"导入FastMCP服务器失败: {e}")
        logger.error("请确保已安装MCP Python SDK: pip install mcp")
        sys.exit(1)
    except Exception as e:
        logger.error(f"服务器运行失败: {e}")
        sys.exit(1)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="启动Codebase QA MCP服务器")
    
    # 基本配置
    parser.add_argument("--repo-path", help="代码库路径")
    parser.add_argument("--persist-path", default="./temp", help="索引持久化路径")
    parser.add_argument("--pipeline-type", choices=["simple", "pro", "balance", "best"], 
                       default="pro", help="Pipeline类型")
    
    # 服务器配置
    parser.add_argument("--server-type", choices=["stdio", "fastmcp"], default="fastmcp",
                       help="服务器类型")
    parser.add_argument("--transport", choices=["stdio", "sse", "streamable-http"], 
                       default="stdio", help="传输方式")
    parser.add_argument("--host", default="localhost", help="服务器主机")
    parser.add_argument("--port", type=int, default=3000, help="服务器端口")
    
    # 调试选项
    parser.add_argument("--debug", action="store_true", help="启用调试模式")
    parser.add_argument("--log-level", choices=["DEBUG", "INFO", "WARNING", "ERROR"], 
                       default="INFO", help="日志级别")
    
    args = parser.parse_args()
    
    # 设置日志级别
    import logging
    logging.getLogger().setLevel(getattr(logging, args.log_level))
    
    # 设置环境
    setup_environment(args)
    
    # 启动服务器
    if args.server_type == "stdio":
        run_stdio_server()
    elif args.server_type == "fastmcp":
        run_fastmcp_server(args.transport, args.host, args.port)
    else:
        logger.error(f"不支持的服务器类型: {args.server_type}")
        sys.exit(1)


if __name__ == "__main__":
    main()
