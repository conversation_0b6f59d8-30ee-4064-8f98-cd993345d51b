from typing import List, Dict, Any
from retriever import BM25RetrieverSimple, EmbeddingRetrieverSimple
from pipeline_base import BasePipeline
from utils import setup_logger

logger = setup_logger(__name__)


class Pipeline(BasePipeline):
    """Simple Pipeline实现"""

    def __init__(
        self,
        bm25_persist_path: str = None,
        emb_persist_path: str = None
    ) -> None:
        if not bm25_persist_path or not emb_persist_path:
            raise ValueError("请提供 BM25 和 Embedding 持久化路径")

        super().__init__(
            bm25_persist_path=bm25_persist_path,
            emb_persist_path=emb_persist_path
        )

    def _create_bm25_retriever(self, persist_path: str):
        """创建BM25检索器"""
        return BM25RetrieverSimple(persist_path)

    def _create_embedding_retriever(self, persist_path: str):
        """创建Embedding检索器"""
        return EmbeddingRetrieverSimple(persist_path)

    def run(self, query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """
        运行Simple Pipeline

        Args:
            query: 查询字符串
            top_k: 返回结果数量

        Returns:
            检索结果列表
        """
        logger.info(f"开始执行Simple Pipeline查询: '{query}'")

        # Step 1: 查询增强
        augmented_query = self.query_augment.augment(query)
        logger.debug(f"查询增强: '{query}' -> '{augmented_query}'")

        # Step 2: 检索
        bm25_results = self._retrieve_bm25(augmented_query)
        embedding_results = self._retrieve_embedding(augmented_query)

        # Step 3: 合并结果
        merged_results = self.result_merger.merge_results(
            bm25_results=bm25_results,
            embedding_results=embedding_results
        )

        # Step 4: 重排序
        reranked_results = self._rerank_results(augmented_query, merged_results)

        # Step 5: 返回结果
        final_results = reranked_results[:top_k]
        result_dicts = [result.model_dump() for result in final_results]

        logger.info(f"Simple Pipeline完成，返回 {len(result_dicts)} 条结果")
        return result_dicts


if __name__ == "__main__":
    bm25_persist_path = "/Users/<USER>/projects/code_qa/CodebaseV2/temp/bm25_index_simple/chainlit"
    emb_persist_path = "/Users/<USER>/projects/code_qa/CodebaseV2/temp/embedding_index_simple/chainlit"
    pipeline = Pipeline(bm25_persist_path, emb_persist_path)
    query = "我想查询和ChainlitEmitter相关的内容"
    res = pipeline.run(query)
    from pprint import pprint

    pprint(res)
