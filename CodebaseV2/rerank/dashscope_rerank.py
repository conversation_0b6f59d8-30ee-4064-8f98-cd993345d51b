import dashscope
import logging

from http import HTTPStatus

# 配置日志
logger = logging.getLogger(__name__)


# 如果没有配置根日志器，则进行基本配置
if not logging.getLogger().handlers:
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(filename)s:%(lineno)s - %(levelname)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )


def rerank(
    query,
    documents: list,
    model_name: str = "gte-rerank-v2",
    top_n=10,
    threshold=0.1,
):
    resp = dashscope.TextReRank.call(
        model=model_name,
        query=query,
        documents=documents,
        # top_n=10,
        top_n=top_n,
        return_documents=True,
    )
    if resp.status_code == HTTPStatus.OK:
        results = resp["output"]["results"]
        results = [
            (item["index"], item["document"]["text"])
            for item in results
            if item["relevance_score"] > threshold
        ]
        return results


if __name__ == __name__:
    print(
        rerank(
            "如何在python中使用多线程",
            ["Rust 多线程", "Python 多进程", "Python 协程", "python 多线程"],
            "gte-rerank-v2",
        )
    )
