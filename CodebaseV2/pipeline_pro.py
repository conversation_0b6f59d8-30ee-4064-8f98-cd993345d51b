import logging
from retriever import BM25<PERSON><PERSON>riever<PERSON><PERSON> as BM25Retriever
from retriever import EmbeddingRetrieverPro as EmbeddingRetriever
from rerank.dashscope_rerank import rerank
from pydantic import BaseModel
from retriever import GraphRetriever


# 配置日志
logger = logging.getLogger(__name__)


# 如果没有配置根日志器，则进行基本配置
if not logging.getLogger().handlers:
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(filename)s:%(lineno)s - %(levelname)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )


class QueryAugment:
    def augment(self, query):
        return query


class ResponseScheme(BaseModel):
    metadata: dict
    file_path: str
    text: str
    score: float


def process_results(results):
    """
    处理检索结果，将其转换为 ResponseScheme 格式
    """
    processed_results = []
    for result in results:
        processed_result = ResponseScheme(
            metadata=result.metadata,
            file_path=result.metadata["file_path"],
            text=result.text,
            score=result.score,
        )
        processed_results.append(processed_result)
    return processed_results


class Pipeline:
    def __init__(
        self,
        bm25_persist_path: str = None,
        emb_persist_path: str = None,
        graph_persist_path: str = None,
    ) -> None:
        self.bm25_persist_path = bm25_persist_path
        self.emb_persist_path = emb_persist_path
        self.graph_persist_path = graph_persist_path

        self.bm25_retriever = None
        self.emb_retriever = None
        self.graph_retriever = None

        self.query_augment = QueryAugment()

        if not self.bm25_persist_path:
            logger.error("未提供 BM25 持久化路径")
        else:
            self.bm25_retriever = BM25Retriever(self.bm25_persist_path)
            logger.info(
                f"成功从 {self.bm25_persist_path} 加载了索引, 启用BM25搜索"
            )

        if not self.emb_persist_path:
            logger.error("未提供 Embedding 持久化路径")
        else:
            self.emb_retriever = EmbeddingRetriever(self.emb_persist_path)
            logger.info(
                f"成功从 {self.emb_persist_path} 加载了索引, 启用embedding搜索"
            )

        if not self.graph_persist_path:
            logger.error("未提供 Graph 持久化路径")
        else:
            self.graph_retriever = GraphRetriever(self.graph_persist_path)
            logger.info(
                f"成功从 {self.graph_persist_path} 加载了索引, 启用graph搜索"
            )

    @staticmethod
    def show_results(results):
        for item in results:
            logger.debug(item)

    def run(self, query, top_k=5):
        # step2 augment query
        query = self.query_augment.augment(query)
        # step3 retrieve
        bm25_results, emb_results = [], []
        if self.bm25_retriever:
            bm25_results = self.bm25_retriever.retrieve(query)
            logger.debug(
                f"BM25 results ({len(bm25_results)}):".center(100, "-")
            )
            self.show_results(bm25_results)
            logger.info(f"BM25检索到 {len(bm25_results)} 条结果")

        if self.emb_retriever:
            emb_results = self.emb_retriever.retrieve(query)
            logger.debug(
                f"Embedding results ({len(emb_results)}):".center(100, "-")
            )
            self.show_results(emb_results)
            logger.info(f"Embedding检索到 {len(emb_results)} 条结果")

        if self.graph_retriever:
            graph_results = self.graph_retriever.retrieve(query)
            logger.debug(
                f"Graph results ({len(graph_results)}):".center(100, "-")
            )
            self.show_results(graph_results)
            logger.info(f"Graph检索到 {len(graph_results)} 条结果")

        # step4 merge results
        results = bm25_results + emb_results
        logger.info(f"合并检索到 {len(results)} 条结果")

        # step5 sort results
        # results = sorted(results, key=lambda x: x.score, reverse=True)
        # documents = [item.get_text() for item in results]
        # logger.debug(f"Start rerank with {len(documents)} documents")
        # rerank_results = rerank(query, documents)
        # results = [results[index] for index, _ in rerank_results]

        # step6 process results
        results = process_results(results)

        # step6 return results
        results = [item.model_dump() for item in results]
        return results[:top_k]


if __name__ == "__main__":
    bm25_persist_path = "/Users/<USER>/projects/code_qa/CodebaseV2/temp/bm25_index_pro/chainlit"
    emb_persist_path = "/Users/<USER>/projects/code_qa/CodebaseV2/temp/embedding_index_pro/chainlit"
    pipeline = Pipeline(bm25_persist_path, emb_persist_path)
    query = "我想查询和ChainlitEmitter相关的内容"
    res = pipeline.run(query, top_k=10)
    from pprint import pprint

    pprint(res)
