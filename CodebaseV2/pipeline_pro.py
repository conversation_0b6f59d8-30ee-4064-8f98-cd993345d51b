from typing import List, Dict, Any
from retriever import BM25RetrieverPro, EmbeddingRetrieverPro, GraphRetriever
from pipeline_base import BasePipeline
from utils import setup_logger

logger = setup_logger(__name__)


class Pipeline(BasePipeline):
    """Pro Pipeline实现"""

    def __init__(
        self,
        bm25_persist_path: str = None,
        emb_persist_path: str = None,
        graph_persist_path: str = None,
    ) -> None:
        super().__init__(
            bm25_persist_path=bm25_persist_path,
            emb_persist_path=emb_persist_path,
            graph_persist_path=graph_persist_path
        )

    def _create_bm25_retriever(self, persist_path: str):
        """创建BM25检索器"""
        return BM25RetrieverPro(persist_path)

    def _create_embedding_retriever(self, persist_path: str):
        """创建Embedding检索器"""
        return EmbeddingRetrieverPro(persist_path)

    def run(self, query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """
        运行Pro Pipeline

        Args:
            query: 查询字符串
            top_k: 返回结果数量

        Returns:
            检索结果列表
        """
        logger.info(f"开始执行Pro Pipeline查询: '{query}'")

        # Step 1: 查询增强
        augmented_query = self.query_augment.augment(query)
        logger.debug(f"查询增强: '{query}' -> '{augmented_query}'")

        # Step 2: 检索
        bm25_results = self._retrieve_bm25(augmented_query)
        embedding_results = self._retrieve_embedding(augmented_query)
        graph_results = self._retrieve_graph(augmented_query)

        # Step 3: 合并结果（Pro版本不启用重排序）
        merged_results = self.result_merger.merge_results(
            bm25_results=bm25_results,
            embedding_results=embedding_results,
            graph_results=graph_results
        )

        # Step 4: 返回结果（Pro版本跳过重排序以提高速度）
        final_results = merged_results[:top_k]
        result_dicts = [result.model_dump() for result in final_results]

        logger.info(f"Pro Pipeline完成，返回 {len(result_dicts)} 条结果")
        return result_dicts


if __name__ == "__main__":
    bm25_persist_path = "/Users/<USER>/projects/code_qa/CodebaseV2/temp/bm25_index_pro/chainlit"
    emb_persist_path = "/Users/<USER>/projects/code_qa/CodebaseV2/temp/embedding_index_pro/chainlit"
    pipeline = Pipeline(bm25_persist_path, emb_persist_path)
    query = "我想查询和ChainlitEmitter相关的内容"
    res = pipeline.run(query, top_k=10)
    from pprint import pprint

    pprint(res)
