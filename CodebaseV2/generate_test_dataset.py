"""
为requests库生成测试评测数据集
"""
import json
import os
from typing import List, Dict, Any
from benchmark.dataset import (
    DatasetBuilder, GroundTruthItem, QueryType, DifficultyLevel
)
from local_model_adapter import LocalLLMClient


def create_requests_dataset() -> List[Dict[str, Any]]:
    """创建requests库的评测数据集"""
    
    # 手工精心设计的查询-答案对，确保高质量
    queries = [
        {
            "id": "req_001",
            "query": "如何发送HTTP GET请求",
            "type": QueryType.API_USAGE,
            "difficulty": DifficultyLevel.EASY,
            "ground_truth": [
                {
                    "file_path": "src/requests/api.py",
                    "start_line": 1,
                    "end_line": 50,
                    "relevance_score": 1.0,
                    "explanation": "包含get函数的定义和实现"
                },
                {
                    "file_path": "src/requests/__init__.py",
                    "start_line": 1,
                    "end_line": 30,
                    "relevance_score": 0.8,
                    "explanation": "导出get函数的模块入口"
                }
            ]
        },
        {
            "id": "req_002", 
            "query": "如何处理HTTP响应",
            "type": QueryType.CLASS_SEARCH,
            "difficulty": DifficultyLevel.MEDIUM,
            "ground_truth": [
                {
                    "file_path": "src/requests/models.py",
                    "start_line": 1,
                    "end_line": 200,
                    "relevance_score": 1.0,
                    "explanation": "Response类的完整定义"
                }
            ]
        },
        {
            "id": "req_003",
            "query": "Session会话管理的实现",
            "type": QueryType.CLASS_SEARCH,
            "difficulty": DifficultyLevel.HARD,
            "ground_truth": [
                {
                    "file_path": "src/requests/sessions.py",
                    "start_line": 1,
                    "end_line": 500,
                    "relevance_score": 1.0,
                    "explanation": "Session类的完整实现"
                }
            ]
        },
        {
            "id": "req_004",
            "query": "HTTP适配器的作用",
            "type": QueryType.CONCEPT_SEARCH,
            "difficulty": DifficultyLevel.HARD,
            "ground_truth": [
                {
                    "file_path": "src/requests/adapters.py",
                    "start_line": 1,
                    "end_line": 300,
                    "relevance_score": 1.0,
                    "explanation": "HTTPAdapter类的实现"
                }
            ]
        },
        {
            "id": "req_005",
            "query": "如何处理认证",
            "type": QueryType.FEATURE_IMPLEMENTATION,
            "difficulty": DifficultyLevel.MEDIUM,
            "ground_truth": [
                {
                    "file_path": "src/requests/auth.py",
                    "start_line": 1,
                    "end_line": 200,
                    "relevance_score": 1.0,
                    "explanation": "认证相关类的实现"
                }
            ]
        },
        {
            "id": "req_006",
            "query": "Cookie处理机制",
            "type": QueryType.CONCEPT_SEARCH,
            "difficulty": DifficultyLevel.MEDIUM,
            "ground_truth": [
                {
                    "file_path": "src/requests/cookies.py",
                    "start_line": 1,
                    "end_line": 400,
                    "relevance_score": 1.0,
                    "explanation": "Cookie处理的完整实现"
                }
            ]
        },
        {
            "id": "req_007",
            "query": "异常处理和错误类型",
            "type": QueryType.BUG_LOCATION,
            "difficulty": DifficultyLevel.EASY,
            "ground_truth": [
                {
                    "file_path": "src/requests/exceptions.py",
                    "start_line": 1,
                    "end_line": 150,
                    "relevance_score": 1.0,
                    "explanation": "所有异常类的定义"
                }
            ]
        },
        {
            "id": "req_008",
            "query": "如何发送POST请求",
            "type": QueryType.API_USAGE,
            "difficulty": DifficultyLevel.EASY,
            "ground_truth": [
                {
                    "file_path": "src/requests/api.py",
                    "start_line": 50,
                    "end_line": 100,
                    "relevance_score": 1.0,
                    "explanation": "post函数的定义"
                }
            ]
        },
        {
            "id": "req_009",
            "query": "工具函数和辅助方法",
            "type": QueryType.FUNCTION_SEARCH,
            "difficulty": DifficultyLevel.MEDIUM,
            "ground_truth": [
                {
                    "file_path": "src/requests/utils.py",
                    "start_line": 1,
                    "end_line": 800,
                    "relevance_score": 1.0,
                    "explanation": "各种工具函数的实现"
                }
            ]
        },
        {
            "id": "req_010",
            "query": "HTTP状态码处理",
            "type": QueryType.CONCEPT_SEARCH,
            "difficulty": DifficultyLevel.EASY,
            "ground_truth": [
                {
                    "file_path": "src/requests/status_codes.py",
                    "start_line": 1,
                    "end_line": 100,
                    "relevance_score": 1.0,
                    "explanation": "状态码相关的定义"
                }
            ]
        },
        {
            "id": "req_011",
            "query": "数据结构和容器类",
            "type": QueryType.CLASS_SEARCH,
            "difficulty": DifficultyLevel.MEDIUM,
            "ground_truth": [
                {
                    "file_path": "src/requests/structures.py",
                    "start_line": 1,
                    "end_line": 200,
                    "relevance_score": 1.0,
                    "explanation": "CaseInsensitiveDict等数据结构"
                }
            ]
        },
        {
            "id": "req_012",
            "query": "钩子函数机制",
            "type": QueryType.FEATURE_IMPLEMENTATION,
            "difficulty": DifficultyLevel.HARD,
            "ground_truth": [
                {
                    "file_path": "src/requests/hooks.py",
                    "start_line": 1,
                    "end_line": 50,
                    "relevance_score": 1.0,
                    "explanation": "钩子函数的实现"
                }
            ]
        },
        {
            "id": "req_013",
            "query": "兼容性处理代码",
            "type": QueryType.CONCEPT_SEARCH,
            "difficulty": DifficultyLevel.MEDIUM,
            "ground_truth": [
                {
                    "file_path": "src/requests/compat.py",
                    "start_line": 1,
                    "end_line": 100,
                    "relevance_score": 1.0,
                    "explanation": "Python版本兼容性处理"
                }
            ]
        },
        {
            "id": "req_014",
            "query": "SSL证书处理",
            "type": QueryType.FEATURE_IMPLEMENTATION,
            "difficulty": DifficultyLevel.MEDIUM,
            "ground_truth": [
                {
                    "file_path": "src/requests/certs.py",
                    "start_line": 1,
                    "end_line": 50,
                    "relevance_score": 1.0,
                    "explanation": "SSL证书相关功能"
                }
            ]
        },
        {
            "id": "req_015",
            "query": "如何设置请求头",
            "type": QueryType.API_USAGE,
            "difficulty": DifficultyLevel.EASY,
            "ground_truth": [
                {
                    "file_path": "src/requests/models.py",
                    "start_line": 200,
                    "end_line": 300,
                    "relevance_score": 0.9,
                    "explanation": "Request类中headers的处理"
                },
                {
                    "file_path": "src/requests/sessions.py",
                    "start_line": 300,
                    "end_line": 400,
                    "relevance_score": 0.8,
                    "explanation": "Session中headers的合并逻辑"
                }
            ]
        },
        {
            "id": "req_016",
            "query": "超时设置和处理",
            "type": QueryType.FEATURE_IMPLEMENTATION,
            "difficulty": DifficultyLevel.MEDIUM,
            "ground_truth": [
                {
                    "file_path": "src/requests/adapters.py",
                    "start_line": 200,
                    "end_line": 300,
                    "relevance_score": 1.0,
                    "explanation": "适配器中的超时处理逻辑"
                }
            ]
        },
        {
            "id": "req_017",
            "query": "代理服务器配置",
            "type": QueryType.CONCEPT_SEARCH,
            "difficulty": DifficultyLevel.HARD,
            "ground_truth": [
                {
                    "file_path": "src/requests/adapters.py",
                    "start_line": 100,
                    "end_line": 200,
                    "relevance_score": 1.0,
                    "explanation": "代理配置相关代码"
                },
                {
                    "file_path": "src/requests/sessions.py",
                    "start_line": 400,
                    "end_line": 500,
                    "relevance_score": 0.7,
                    "explanation": "Session中的代理处理"
                }
            ]
        },
        {
            "id": "req_018",
            "query": "文件上传功能",
            "type": QueryType.API_USAGE,
            "difficulty": DifficultyLevel.MEDIUM,
            "ground_truth": [
                {
                    "file_path": "src/requests/models.py",
                    "start_line": 300,
                    "end_line": 400,
                    "relevance_score": 1.0,
                    "explanation": "Request类中文件处理逻辑"
                }
            ]
        },
        {
            "id": "req_019",
            "query": "重定向处理机制",
            "type": QueryType.FEATURE_IMPLEMENTATION,
            "difficulty": DifficultyLevel.HARD,
            "ground_truth": [
                {
                    "file_path": "src/requests/sessions.py",
                    "start_line": 100,
                    "end_line": 200,
                    "relevance_score": 1.0,
                    "explanation": "Session中的重定向处理逻辑"
                }
            ]
        },
        {
            "id": "req_020",
            "query": "编码和解码处理",
            "type": QueryType.CONCEPT_SEARCH,
            "difficulty": DifficultyLevel.MEDIUM,
            "ground_truth": [
                {
                    "file_path": "src/requests/utils.py",
                    "start_line": 400,
                    "end_line": 600,
                    "relevance_score": 1.0,
                    "explanation": "编码相关的工具函数"
                },
                {
                    "file_path": "src/requests/models.py",
                    "start_line": 400,
                    "end_line": 500,
                    "relevance_score": 0.8,
                    "explanation": "Response中的编码处理"
                }
            ]
        }
    ]
    
    return queries


def save_dataset_as_jsonl(queries: List[Dict[str, Any]], output_path: str):
    """保存为JSONL格式"""
    with open(output_path, 'w', encoding='utf-8') as f:
        for query in queries:
            # 转换为JSONL格式
            jsonl_item = {
                "id": query["id"],
                "query": query["query"],
                "query_type": query["type"].value,
                "difficulty": query["difficulty"].value,
                "ground_truth": [
                    {
                        "file_path": gt["file_path"],
                        "start_line": gt.get("start_line"),
                        "end_line": gt.get("end_line"),
                        "relevance_score": gt["relevance_score"],
                        "explanation": gt["explanation"]
                    }
                    for gt in query["ground_truth"]
                ]
            }
            f.write(json.dumps(jsonl_item, ensure_ascii=False) + '\n')


def main():
    """主函数"""
    print("生成requests库评测数据集...")
    
    # 生成查询数据
    queries = create_requests_dataset()
    
    # 保存为JSONL格式
    output_path = "requests_test_dataset.jsonl"
    save_dataset_as_jsonl(queries, output_path)
    
    print(f"✅ 生成完成！")
    print(f"📁 输出文件: {output_path}")
    print(f"📊 查询数量: {len(queries)}")
    
    # 统计信息
    type_counts = {}
    difficulty_counts = {}
    
    for query in queries:
        query_type = query["type"].value
        difficulty = query["difficulty"].value
        
        type_counts[query_type] = type_counts.get(query_type, 0) + 1
        difficulty_counts[difficulty] = difficulty_counts.get(difficulty, 0) + 1
    
    print("\n📈 统计信息:")
    print("查询类型分布:")
    for qtype, count in type_counts.items():
        print(f"  {qtype}: {count}")
    
    print("难度分布:")
    for difficulty, count in difficulty_counts.items():
        print(f"  {difficulty}: {count}")
    
    # 同时保存为标准JSON格式用于评测系统
    builder = DatasetBuilder(
        name="requests_test_dataset",
        version="1.0.0",
        description="Requests库测试评测数据集",
        repo_path="../test_repo"
    )
    
    for query in queries:
        ground_truth = [
            GroundTruthItem(
                file_path=gt["file_path"],
                start_line=gt.get("start_line"),
                end_line=gt.get("end_line"),
                relevance_score=gt["relevance_score"],
                explanation=gt["explanation"]
            )
            for gt in query["ground_truth"]
        ]
        
        builder.add_query(
            query_id=query["id"],
            query_text=query["query"],
            query_type=query["type"],
            difficulty=query["difficulty"],
            ground_truth=ground_truth
        )
    
    dataset = builder.build()
    dataset.save("requests_test_dataset.json")
    print(f"📁 标准格式: requests_test_dataset.json")


if __name__ == "__main__":
    main()
