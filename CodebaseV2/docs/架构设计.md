# index build
index或者数据构建是一个pipeline的前置任务，是必要要做的。应该和pipeline流程割裂，保留检测部分。

# pipeline 
pipeline 的数据对象是query，包含query返回结果的处理，过程对象为整个Search过程，返回结果就是查询结果。

# build和search是否需要在一个流水线完成？
这个问题其实需要回答，我们的入口是什么？我们提供的交互可能性是什么？
这里，build功能是需要拆解出来的，因为我们的build是一个耗时的过程，而且是一个一次性的过程，所以我们需要把build的过程拆解出来，并且提供一个接口，让用户可以自己控制build的过程。

在未来，数据的增量更新过程，也希望这个过程解耦，不应该串联到search的过程中，应该自己去维护数据。
另外，search会使用build的结果，他们需要保持一定时序上一致性。

对于search，search和build的交互只在数据格式和数据的存储，因此，search只需要知道数据的格式和数据的存储，就可以了。

# 对于seach过程，为什么任务其是一个pipeline？
其实任何一个多任务系统都可以认为是一个pipeline，agent也是。在命名上，使用agent还是pipeline我认为都是一样的。
那么search设计很多子任务吗？我觉得现在这个阶段，search的流程还未固化，甚至很多东西都还不会稳定，所以我觉得现在的设计是一个比较好的设计。
在未来，如果我们能对当前search的过程进行一个固化，那么就可以将pipeline转换成一个Search类对象。因此对于无特征的多任务系统，我们还是以pipeline的方式命名比较好。并且，search过程涉及的流程很多，所以pipeline是一个比较好的命名。

# 未来我们应该如何去做迭代更新呢？
我认为，假如整体流程不变，只是具体实现的细节发生变化，我期望不修改的其命名。对于差异性比较大的情况，我期望新建一个对象，这样就能保证代码的可维护性。


