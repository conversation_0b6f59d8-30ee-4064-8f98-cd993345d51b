"""
评测数据集定义和管理
"""
from typing import List, Dict, Any, Optional, Union
from pydantic import BaseModel, Field
from enum import Enum
import json
import os
from pathlib import Path


class QueryType(str, Enum):
    """查询类型"""
    FUNCTION_SEARCH = "function_search"  # 函数搜索
    CLASS_SEARCH = "class_search"        # 类搜索
    CONCEPT_SEARCH = "concept_search"    # 概念搜索
    API_USAGE = "api_usage"              # API使用
    BUG_LOCATION = "bug_location"        # Bug定位
    FEATURE_IMPLEMENTATION = "feature_implementation"  # 功能实现
    CODE_UNDERSTANDING = "code_understanding"  # 代码理解


class DifficultyLevel(str, Enum):
    """难度级别"""
    EASY = "easy"      # 简单：直接的函数名或类名搜索
    MEDIUM = "medium"  # 中等：需要理解概念或上下文
    HARD = "hard"      # 困难：需要深度理解和推理


class GroundTruthItem(BaseModel):
    """标准答案项"""
    file_path: str = Field(description="文件路径")
    start_line: Optional[int] = Field(default=None, description="起始行号")
    end_line: Optional[int] = Field(default=None, description="结束行号")
    relevance_score: float = Field(ge=0.0, le=1.0, description="相关性分数 (0-1)")
    explanation: Optional[str] = Field(default=None, description="为什么相关的解释")


class QueryItem(BaseModel):
    """查询项"""
    id: str = Field(description="查询ID")
    query: str = Field(description="查询文本")
    query_type: QueryType = Field(description="查询类型")
    difficulty: DifficultyLevel = Field(description="难度级别")
    language: str = Field(default="zh", description="查询语言")
    ground_truth: List[GroundTruthItem] = Field(description="标准答案列表")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="额外元数据")


class Dataset(BaseModel):
    """评测数据集"""
    name: str = Field(description="数据集名称")
    version: str = Field(description="版本号")
    description: str = Field(description="数据集描述")
    repo_info: Dict[str, Any] = Field(description="代码库信息")
    queries: List[QueryItem] = Field(description="查询列表")
    statistics: Dict[str, Any] = Field(default_factory=dict, description="统计信息")
    
    def save(self, file_path: Union[str, Path]):
        """保存数据集到文件"""
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(self.model_dump(), f, indent=2, ensure_ascii=False)
    
    @classmethod
    def load(cls, file_path: Union[str, Path]) -> "Dataset":
        """从文件加载数据集"""
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        return cls(**data)
    
    def get_statistics(self) -> Dict[str, Any]:
        """计算数据集统计信息"""
        stats = {
            "total_queries": len(self.queries),
            "query_types": {},
            "difficulty_levels": {},
            "languages": {},
            "avg_ground_truth_items": 0,
            "total_ground_truth_items": 0
        }
        
        total_gt_items = 0
        for query in self.queries:
            # 查询类型统计
            query_type = query.query_type.value
            stats["query_types"][query_type] = stats["query_types"].get(query_type, 0) + 1
            
            # 难度级别统计
            difficulty = query.difficulty.value
            stats["difficulty_levels"][difficulty] = stats["difficulty_levels"].get(difficulty, 0) + 1
            
            # 语言统计
            language = query.language
            stats["languages"][language] = stats["languages"].get(language, 0) + 1
            
            # 标准答案统计
            gt_count = len(query.ground_truth)
            total_gt_items += gt_count
        
        stats["total_ground_truth_items"] = total_gt_items
        stats["avg_ground_truth_items"] = total_gt_items / len(self.queries) if self.queries else 0
        
        self.statistics = stats
        return stats
    
    def filter_by_type(self, query_type: QueryType) -> "Dataset":
        """按查询类型过滤"""
        filtered_queries = [q for q in self.queries if q.query_type == query_type]
        return Dataset(
            name=f"{self.name}_filtered_{query_type.value}",
            version=self.version,
            description=f"{self.description} (filtered by {query_type.value})",
            repo_info=self.repo_info,
            queries=filtered_queries
        )
    
    def filter_by_difficulty(self, difficulty: DifficultyLevel) -> "Dataset":
        """按难度级别过滤"""
        filtered_queries = [q for q in self.queries if q.difficulty == difficulty]
        return Dataset(
            name=f"{self.name}_filtered_{difficulty.value}",
            version=self.version,
            description=f"{self.description} (filtered by {difficulty.value})",
            repo_info=self.repo_info,
            queries=filtered_queries
        )


class DatasetBuilder:
    """数据集构建器"""
    
    def __init__(self, name: str, version: str, description: str, repo_path: str):
        self.name = name
        self.version = version
        self.description = description
        self.repo_path = repo_path
        self.queries: List[QueryItem] = []
        
        # 获取代码库信息
        self.repo_info = {
            "path": repo_path,
            "name": os.path.basename(repo_path),
            "exists": os.path.exists(repo_path)
        }
    
    def add_query(
        self,
        query_id: str,
        query_text: str,
        query_type: QueryType,
        difficulty: DifficultyLevel,
        ground_truth: List[GroundTruthItem],
        language: str = "zh",
        metadata: Optional[Dict[str, Any]] = None
    ):
        """添加查询项"""
        query_item = QueryItem(
            id=query_id,
            query=query_text,
            query_type=query_type,
            difficulty=difficulty,
            language=language,
            ground_truth=ground_truth,
            metadata=metadata or {}
        )
        self.queries.append(query_item)
    
    def build(self) -> Dataset:
        """构建数据集"""
        dataset = Dataset(
            name=self.name,
            version=self.version,
            description=self.description,
            repo_info=self.repo_info,
            queries=self.queries
        )
        dataset.get_statistics()
        return dataset


# 预定义的查询模板
QUERY_TEMPLATES = {
    QueryType.FUNCTION_SEARCH: [
        "如何实现{function_name}功能",
        "找到{function_name}函数的定义",
        "{function_name}函数在哪里",
        "搜索{function_name}相关的代码"
    ],
    QueryType.CLASS_SEARCH: [
        "找到{class_name}类的定义",
        "{class_name}类的实现在哪里",
        "搜索{class_name}相关的代码",
        "{class_name}类有哪些方法"
    ],
    QueryType.CONCEPT_SEARCH: [
        "如何处理{concept}",
        "{concept}相关的实现",
        "找到{concept}的代码",
        "{concept}功能是如何实现的"
    ],
    QueryType.API_USAGE: [
        "如何使用{api_name} API",
        "{api_name}的使用示例",
        "调用{api_name}的代码",
        "{api_name} API的实现"
    ],
    QueryType.BUG_LOCATION: [
        "可能导致{error_type}错误的代码",
        "查找{error_type}相关的问题",
        "{error_type}错误的处理",
        "修复{error_type}的代码"
    ],
    QueryType.FEATURE_IMPLEMENTATION: [
        "如何实现{feature_name}功能",
        "{feature_name}的具体实现",
        "{feature_name}功能的代码",
        "实现{feature_name}的方法"
    ],
    QueryType.CODE_UNDERSTANDING: [
        "这段代码的作用是什么",
        "解释{code_snippet}的功能",
        "{module_name}模块的作用",
        "理解{component_name}组件"
    ]
}


def create_sample_dataset() -> Dataset:
    """创建示例数据集"""
    builder = DatasetBuilder(
        name="sample_codebase_qa",
        version="1.0.0",
        description="示例代码库问答数据集",
        repo_path="/path/to/sample/repo"
    )
    
    # 添加示例查询
    builder.add_query(
        query_id="q001",
        query_text="如何实现用户认证功能",
        query_type=QueryType.FEATURE_IMPLEMENTATION,
        difficulty=DifficultyLevel.MEDIUM,
        ground_truth=[
            GroundTruthItem(
                file_path="src/auth/authentication.py",
                start_line=10,
                end_line=50,
                relevance_score=0.9,
                explanation="主要的用户认证实现"
            ),
            GroundTruthItem(
                file_path="src/auth/models.py",
                start_line=1,
                end_line=30,
                relevance_score=0.7,
                explanation="用户模型定义"
            )
        ]
    )
    
    builder.add_query(
        query_id="q002",
        query_text="找到数据库连接的代码",
        query_type=QueryType.CONCEPT_SEARCH,
        difficulty=DifficultyLevel.EASY,
        ground_truth=[
            GroundTruthItem(
                file_path="src/database/connection.py",
                start_line=1,
                end_line=100,
                relevance_score=1.0,
                explanation="数据库连接的完整实现"
            )
        ]
    )
    
    return builder.build()


if __name__ == "__main__":
    # 创建示例数据集
    dataset = create_sample_dataset()
    
    # 保存数据集
    dataset.save("sample_dataset.json")
    
    # 打印统计信息
    stats = dataset.get_statistics()
    print("数据集统计信息:")
    print(json.dumps(stats, indent=2, ensure_ascii=False))
