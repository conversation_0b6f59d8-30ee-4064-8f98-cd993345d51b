"""
评测语料生成工具
基于LLM自动生成高质量的查询-答案对
"""
import os
import json
import random
import asyncio
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path

try:
    import openai
    HAS_OPENAI = True
except ImportError:
    HAS_OPENAI = False

from dataset import (
    Dataset, DatasetBuilder, GroundTruthItem, 
    QueryType, DifficultyLevel
)
from dataset_generator import CodeAnalyzer
from utils import setup_logger

logger = setup_logger(__name__)


class LLMQueryGenerator:
    """基于LLM的查询生成器"""
    
    def __init__(self, api_key: Optional[str] = None, model: str = "gpt-3.5-turbo"):
        if not HAS_OPENAI:
            raise ImportError("请安装openai库: pip install openai")
        
        self.client = openai.OpenAI(api_key=api_key or os.getenv("OPENAI_API_KEY"))
        self.model = model
    
    async def generate_queries_for_function(
        self, 
        function_info: Dict[str, Any], 
        file_path: str,
        context: str = ""
    ) -> List[Dict[str, Any]]:
        """为函数生成查询"""
        
        prompt = f"""
基于以下Python函数信息，生成3-5个不同难度的自然语言查询。这些查询应该能够帮助用户找到这个函数。

函数信息:
- 函数名: {function_info['name']}
- 文件路径: {file_path}
- 参数: {function_info.get('args', [])}
- 文档字符串: {function_info.get('docstring', '无')}

上下文信息:
{context}

请生成查询，包含以下类型:
1. 直接的函数名搜索 (简单)
2. 基于功能描述的搜索 (中等)
3. 基于使用场景的搜索 (困难)

返回JSON格式，包含以下字段:
- query: 查询文本
- difficulty: "easy", "medium", "hard"
- explanation: 为什么这个查询应该找到这个函数

示例格式:
[
  {{
    "query": "如何实现用户登录验证",
    "difficulty": "medium",
    "explanation": "这个查询描述了函数的主要功能"
  }}
]
"""
        
        try:
            response = await self._call_llm(prompt)
            queries = json.loads(response)
            
            # 验证和清理结果
            validated_queries = []
            for query in queries:
                if all(key in query for key in ["query", "difficulty", "explanation"]):
                    validated_queries.append(query)
            
            return validated_queries
            
        except Exception as e:
            logger.error(f"生成函数查询失败: {e}")
            return []
    
    async def generate_queries_for_class(
        self,
        class_info: Dict[str, Any],
        file_path: str,
        context: str = ""
    ) -> List[Dict[str, Any]]:
        """为类生成查询"""
        
        methods_info = "\n".join([f"- {method['name']}" for method in class_info.get('methods', [])])
        
        prompt = f"""
基于以下Python类信息，生成3-5个不同难度的自然语言查询。

类信息:
- 类名: {class_info['name']}
- 文件路径: {file_path}
- 基类: {class_info.get('bases', [])}
- 方法列表:
{methods_info}
- 文档字符串: {class_info.get('docstring', '无')}

上下文信息:
{context}

请生成查询，包含以下类型:
1. 直接的类名搜索 (简单)
2. 基于类功能的搜索 (中等)
3. 基于设计模式或架构的搜索 (困难)

返回JSON格式，包含query, difficulty, explanation字段。
"""
        
        try:
            response = await self._call_llm(prompt)
            queries = json.loads(response)
            
            validated_queries = []
            for query in queries:
                if all(key in query for key in ["query", "difficulty", "explanation"]):
                    validated_queries.append(query)
            
            return validated_queries
            
        except Exception as e:
            logger.error(f"生成类查询失败: {e}")
            return []
    
    async def generate_concept_queries(
        self,
        concept: str,
        related_files: List[str],
        context: str = ""
    ) -> List[Dict[str, Any]]:
        """生成概念相关的查询"""
        
        files_info = "\n".join([f"- {file}" for file in related_files[:10]])
        
        prompt = f"""
基于以下编程概念和相关文件，生成3-5个自然语言查询。

概念: {concept}
相关文件:
{files_info}

上下文信息:
{context}

请生成不同角度的查询:
1. 概念实现的查询
2. 使用方法的查询
3. 问题解决的查询
4. 最佳实践的查询

返回JSON格式，包含query, difficulty, explanation字段。
"""
        
        try:
            response = await self._call_llm(prompt)
            queries = json.loads(response)
            
            validated_queries = []
            for query in queries:
                if all(key in query for key in ["query", "difficulty", "explanation"]):
                    validated_queries.append(query)
            
            return validated_queries
            
        except Exception as e:
            logger.error(f"生成概念查询失败: {e}")
            return []
    
    async def _call_llm(self, prompt: str) -> str:
        """调用LLM API"""
        try:
            response = await asyncio.to_thread(
                self.client.chat.completions.create,
                model=self.model,
                messages=[
                    {"role": "system", "content": "你是一个专业的代码搜索专家，擅长生成高质量的代码搜索查询。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.7,
                max_tokens=1000
            )
            return response.choices[0].message.content
        except Exception as e:
            logger.error(f"LLM API调用失败: {e}")
            raise


class IntelligentDatasetGenerator:
    """智能数据集生成器"""
    
    def __init__(
        self, 
        repo_path: str, 
        dataset_name: str = "intelligent_generated",
        use_llm: bool = True,
        llm_api_key: Optional[str] = None
    ):
        self.repo_path = repo_path
        self.dataset_name = dataset_name
        self.use_llm = use_llm
        
        self.analyzer = CodeAnalyzer(repo_path)
        self.builder = DatasetBuilder(
            name=dataset_name,
            version="1.0.0",
            description=f"智能生成的代码库评测数据集 - {os.path.basename(repo_path)}",
            repo_path=repo_path
        )
        
        if use_llm:
            try:
                self.llm_generator = LLMQueryGenerator(api_key=llm_api_key)
            except ImportError:
                logger.warning("无法使用LLM生成器，将使用基础模板生成")
                self.use_llm = False
    
    async def generate_dataset(self, max_queries: int = 50) -> Dataset:
        """生成智能数据集"""
        logger.info("开始生成智能数据集")
        
        # 扫描代码库
        self.analyzer.scan_repository()
        
        generated_count = 0
        
        if self.use_llm:
            # 使用LLM生成高质量查询
            generated_count += await self._generate_llm_queries(max_queries)
        else:
            # 使用模板生成基础查询
            generated_count += self._generate_template_queries(max_queries)
        
        logger.info(f"生成了 {generated_count} 个查询")
        return self.builder.build()
    
    async def _generate_llm_queries(self, max_queries: int) -> int:
        """使用LLM生成查询"""
        generated = 0
        
        # 获取代码库概览
        context = self._get_repository_context()
        
        # 生成函数查询
        all_functions = self.analyzer.get_all_functions()
        selected_functions = random.sample(
            all_functions, 
            min(max_queries // 3, len(all_functions))
        )
        
        for file_path, func_info in selected_functions:
            if generated >= max_queries:
                break
                
            try:
                queries = await self.llm_generator.generate_queries_for_function(
                    func_info, file_path, context
                )
                
                for query_data in queries:
                    if generated >= max_queries:
                        break
                    
                    self._add_query_from_llm_response(
                        query_data, 
                        QueryType.FUNCTION_SEARCH,
                        file_path,
                        func_info['lineno'],
                        func_info.get('end_lineno', func_info['lineno']),
                        f"func_llm_{generated:03d}"
                    )
                    generated += 1
                    
            except Exception as e:
                logger.error(f"LLM生成函数查询失败: {e}")
                continue
        
        # 生成类查询
        all_classes = self.analyzer.get_all_classes()
        selected_classes = random.sample(
            all_classes,
            min(max_queries // 3, len(all_classes))
        )
        
        for file_path, class_info in selected_classes:
            if generated >= max_queries:
                break
                
            try:
                queries = await self.llm_generator.generate_queries_for_class(
                    class_info, file_path, context
                )
                
                for query_data in queries:
                    if generated >= max_queries:
                        break
                    
                    self._add_query_from_llm_response(
                        query_data,
                        QueryType.CLASS_SEARCH,
                        file_path,
                        class_info['lineno'],
                        class_info.get('end_lineno', class_info['lineno']),
                        f"class_llm_{generated:03d}"
                    )
                    generated += 1
                    
            except Exception as e:
                logger.error(f"LLM生成类查询失败: {e}")
                continue
        
        return generated
    
    def _generate_template_queries(self, max_queries: int) -> int:
        """使用模板生成基础查询"""
        # 这里可以复用之前的模板生成逻辑
        from dataset_generator import DatasetGenerator
        
        basic_generator = DatasetGenerator(self.repo_path, self.dataset_name)
        basic_generator.analyzer = self.analyzer  # 复用已扫描的结果
        
        # 生成基础查询
        generated = 0
        generated += basic_generator._generate_function_queries(max_queries // 4)
        generated += basic_generator._generate_class_queries(max_queries // 4)
        generated += basic_generator._generate_concept_queries(max_queries // 4)
        generated += basic_generator._generate_api_queries(max_queries - generated)
        
        # 将生成的查询添加到当前builder
        for query in basic_generator.builder.queries:
            self.builder.add_query(
                query_id=query.id,
                query_text=query.query,
                query_type=query.query_type,
                difficulty=query.difficulty,
                ground_truth=query.ground_truth,
                metadata=query.metadata
            )
        
        return generated
    
    def _get_repository_context(self) -> str:
        """获取代码库上下文信息"""
        context_parts = []
        
        # 项目基本信息
        context_parts.append(f"项目路径: {self.repo_path}")
        context_parts.append(f"项目名称: {os.path.basename(self.repo_path)}")
        
        # 文件统计
        context_parts.append(f"Python文件数量: {len(self.analyzer.python_files)}")
        
        # 主要目录结构
        directories = set()
        for file_path in self.analyzer.python_files[:20]:  # 限制数量
            dir_path = os.path.dirname(file_path)
            if dir_path:
                directories.add(dir_path.split('/')[0])
        
        if directories:
            context_parts.append(f"主要目录: {', '.join(sorted(directories))}")
        
        return "\n".join(context_parts)
    
    def _add_query_from_llm_response(
        self,
        query_data: Dict[str, Any],
        query_type: QueryType,
        file_path: str,
        start_line: int,
        end_line: int,
        query_id: str
    ):
        """从LLM响应添加查询"""
        difficulty_map = {
            "easy": DifficultyLevel.EASY,
            "medium": DifficultyLevel.MEDIUM,
            "hard": DifficultyLevel.HARD
        }
        
        difficulty = difficulty_map.get(query_data["difficulty"], DifficultyLevel.MEDIUM)
        
        ground_truth = [
            GroundTruthItem(
                file_path=file_path,
                start_line=start_line,
                end_line=end_line,
                relevance_score=1.0,
                explanation=query_data["explanation"]
            )
        ]
        
        self.builder.add_query(
            query_id=query_id,
            query_text=query_data["query"],
            query_type=query_type,
            difficulty=difficulty,
            ground_truth=ground_truth,
            metadata={
                "generated_by": "llm",
                "llm_explanation": query_data["explanation"]
            }
        )


async def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="生成智能评测数据集")
    parser.add_argument("--repo-path", required=True, help="代码库路径")
    parser.add_argument("--output", default="intelligent_dataset.json", help="输出文件路径")
    parser.add_argument("--max-queries", type=int, default=50, help="最大查询数量")
    parser.add_argument("--use-llm", action="store_true", help="使用LLM生成查询")
    parser.add_argument("--api-key", help="OpenAI API密钥")
    
    args = parser.parse_args()
    
    # 生成数据集
    generator = IntelligentDatasetGenerator(
        repo_path=args.repo_path,
        use_llm=args.use_llm,
        llm_api_key=args.api_key
    )
    
    dataset = await generator.generate_dataset(args.max_queries)
    
    # 保存数据集
    dataset.save(args.output)
    
    # 打印统计信息
    stats = dataset.get_statistics()
    print("智能数据集生成完成!")
    print(f"输出文件: {args.output}")
    print("统计信息:")
    for key, value in stats.items():
        print(f"  {key}: {value}")


if __name__ == "__main__":
    asyncio.run(main())
