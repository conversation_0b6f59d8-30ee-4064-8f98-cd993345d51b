"""
评测执行器
执行完整的评测流程，包括数据加载、模型运行、指标计算等
"""
import os
import json
import time
from typing import List, Dict, Any, Optional, Union
from pathlib import Path
from datetime import datetime

from dataset import Dataset, QueryItem
from metrics import MetricsCalculator, MetricsAggregator, convert_pipeline_results_to_retrieval_results
from utils import setup_logger

logger = setup_logger(__name__)


class EvaluationConfig:
    """评测配置"""
    
    def __init__(
        self,
        dataset_path: str,
        output_dir: str = "./evaluation_results",
        k_values: List[int] = None,
        save_detailed_results: bool = True,
        save_failed_queries: bool = True
    ):
        self.dataset_path = dataset_path
        self.output_dir = output_dir
        self.k_values = k_values or [1, 3, 5, 10]
        self.save_detailed_results = save_detailed_results
        self.save_failed_queries = save_failed_queries
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)


class EvaluationResult:
    """评测结果"""
    
    def __init__(self):
        self.query_results: List[Dict[str, Any]] = []
        self.aggregated_metrics: Dict[str, Any] = {}
        self.summary_report: Dict[str, Any] = {}
        self.execution_time: float = 0.0
        self.failed_queries: List[Dict[str, Any]] = []
        self.metadata: Dict[str, Any] = {}
    
    def save(self, output_path: str):
        """保存评测结果"""
        result_data = {
            "query_results": self.query_results,
            "aggregated_metrics": self.aggregated_metrics,
            "summary_report": self.summary_report,
            "execution_time": self.execution_time,
            "failed_queries": self.failed_queries,
            "metadata": self.metadata,
            "timestamp": datetime.now().isoformat()
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(result_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"评测结果已保存到: {output_path}")


class Evaluator:
    """评测器"""
    
    def __init__(self, config: EvaluationConfig):
        self.config = config
        self.metrics_calculator = MetricsCalculator()
        self.metrics_aggregator = MetricsAggregator()
        self.dataset: Optional[Dataset] = None
        self.pipeline = None
    
    def load_dataset(self) -> Dataset:
        """加载数据集"""
        logger.info(f"加载数据集: {self.config.dataset_path}")
        self.dataset = Dataset.load(self.config.dataset_path)
        logger.info(f"数据集加载完成，包含 {len(self.dataset.queries)} 个查询")
        return self.dataset
    
    def set_pipeline(self, pipeline):
        """设置要评测的pipeline"""
        self.pipeline = pipeline
        logger.info(f"设置pipeline: {type(pipeline).__name__}")
    
    def evaluate_single_query(self, query: QueryItem) -> Dict[str, Any]:
        """评测单个查询"""
        logger.debug(f"评测查询: {query.id} - {query.query}")
        
        try:
            start_time = time.time()
            
            # 执行检索
            pipeline_results = self.pipeline.run(query.query, top_k=max(self.config.k_values))
            
            execution_time = time.time() - start_time
            
            # 转换结果格式
            retrieval_results = convert_pipeline_results_to_retrieval_results(pipeline_results)
            
            # 计算指标
            metrics = self.metrics_calculator.calculate_all_metrics(
                retrieval_results, 
                query.ground_truth, 
                self.config.k_values
            )
            
            # 转换指标格式
            metrics_dict = {name: result.value for name, result in metrics.items()}
            
            result = {
                "query_id": query.id,
                "query_text": query.query,
                "query_type": query.query_type.value,
                "difficulty": query.difficulty.value,
                "execution_time": execution_time,
                "metrics": metrics_dict,
                "retrieved_results": [
                    {
                        "file_path": r.file_path,
                        "score": r.score,
                        "rank": r.rank
                    } for r in retrieval_results
                ],
                "ground_truth": [
                    {
                        "file_path": gt.file_path,
                        "relevance_score": gt.relevance_score,
                        "explanation": gt.explanation
                    } for gt in query.ground_truth
                ],
                "success": True
            }
            
            logger.debug(f"查询 {query.id} 评测完成，precision@5: {metrics_dict.get('precision@5', 0):.3f}")
            return result
            
        except Exception as e:
            logger.error(f"查询 {query.id} 评测失败: {e}")
            return {
                "query_id": query.id,
                "query_text": query.query,
                "query_type": query.query_type.value,
                "difficulty": query.difficulty.value,
                "error": str(e),
                "success": False
            }
    
    def evaluate_all(self) -> EvaluationResult:
        """评测所有查询"""
        if not self.dataset:
            raise ValueError("请先加载数据集")
        
        if not self.pipeline:
            raise ValueError("请先设置pipeline")
        
        logger.info("开始完整评测")
        start_time = time.time()
        
        result = EvaluationResult()
        successful_metrics = []
        
        # 评测每个查询
        for i, query in enumerate(self.dataset.queries):
            logger.info(f"评测进度: {i+1}/{len(self.dataset.queries)} - {query.id}")
            
            query_result = self.evaluate_single_query(query)
            result.query_results.append(query_result)
            
            if query_result["success"]:
                # 转换指标格式用于聚合
                metrics_for_aggregation = {}
                for name, value in query_result["metrics"].items():
                    from metrics import MetricResult, MetricType
                    # 简化处理，假设所有指标都是相同类型
                    metrics_for_aggregation[name] = MetricResult(MetricType.PRECISION, value)
                
                successful_metrics.append(metrics_for_aggregation)
            else:
                result.failed_queries.append(query_result)
        
        # 聚合指标
        if successful_metrics:
            result.aggregated_metrics = self.metrics_aggregator.aggregate_metrics(successful_metrics)
            # 转换为可序列化的格式
            aggregated_dict = {}
            for name, metric in result.aggregated_metrics.items():
                aggregated_dict[name] = {
                    "value": metric.value,
                    "details": metric.details
                }
            result.aggregated_metrics = aggregated_dict
            
            # 生成汇总报告
            result.summary_report = self.metrics_aggregator.create_summary_report(
                {name: metric for name, metric in result.aggregated_metrics.items()}
            )
        
        # 记录执行时间和元数据
        result.execution_time = time.time() - start_time
        result.metadata = {
            "dataset_name": self.dataset.name,
            "dataset_version": self.dataset.version,
            "total_queries": len(self.dataset.queries),
            "successful_queries": len(successful_metrics),
            "failed_queries": len(result.failed_queries),
            "pipeline_type": type(self.pipeline).__name__,
            "k_values": self.config.k_values
        }
        
        logger.info(f"评测完成，耗时: {result.execution_time:.2f}秒")
        logger.info(f"成功: {len(successful_metrics)}, 失败: {len(result.failed_queries)}")
        
        return result
    
    def run_evaluation(self) -> EvaluationResult:
        """运行完整评测流程"""
        # 加载数据集
        self.load_dataset()
        
        # 执行评测
        result = self.evaluate_all()
        
        # 保存结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = os.path.join(
            self.config.output_dir, 
            f"evaluation_result_{timestamp}.json"
        )
        result.save(output_file)
        
        # 保存详细结果（可选）
        if self.config.save_detailed_results:
            detailed_file = os.path.join(
                self.config.output_dir,
                f"detailed_results_{timestamp}.json"
            )
            with open(detailed_file, 'w', encoding='utf-8') as f:
                json.dump(result.query_results, f, indent=2, ensure_ascii=False)
        
        # 保存失败查询（可选）
        if self.config.save_failed_queries and result.failed_queries:
            failed_file = os.path.join(
                self.config.output_dir,
                f"failed_queries_{timestamp}.json"
            )
            with open(failed_file, 'w', encoding='utf-8') as f:
                json.dump(result.failed_queries, f, indent=2, ensure_ascii=False)
        
        return result


def run_benchmark(
    pipeline,
    dataset_path: str,
    output_dir: str = "./evaluation_results",
    k_values: List[int] = None
) -> EvaluationResult:
    """
    运行基准测试的便捷函数
    
    Args:
        pipeline: 要评测的pipeline实例
        dataset_path: 数据集文件路径
        output_dir: 输出目录
        k_values: 要计算的k值列表
        
    Returns:
        评测结果
    """
    config = EvaluationConfig(
        dataset_path=dataset_path,
        output_dir=output_dir,
        k_values=k_values
    )
    
    evaluator = Evaluator(config)
    evaluator.set_pipeline(pipeline)
    
    return evaluator.run_evaluation()


if __name__ == "__main__":
    # 示例用法
    import argparse
    
    parser = argparse.ArgumentParser(description="运行代码库检索评测")
    parser.add_argument("--dataset", required=True, help="数据集文件路径")
    parser.add_argument("--output-dir", default="./evaluation_results", help="输出目录")
    parser.add_argument("--pipeline-type", choices=["simple", "pro", "balance", "best"], 
                       default="pro", help="Pipeline类型")
    parser.add_argument("--repo-path", help="代码库路径")
    
    args = parser.parse_args()
    
    # 这里需要根据实际情况导入和初始化pipeline
    print(f"评测配置:")
    print(f"  数据集: {args.dataset}")
    print(f"  输出目录: {args.output_dir}")
    print(f"  Pipeline类型: {args.pipeline_type}")
    print(f"  代码库路径: {args.repo_path}")
    
    print("请在实际使用时导入相应的pipeline并运行评测")
