"""
预定义的评测数据集
包含针对不同领域的高质量查询-答案对
"""
from typing import List
from dataset import (
    Dataset, DatasetBuilder, GroundTruthItem,
    QueryType, DifficultyLevel
)


def create_python_web_dataset(repo_path: str) -> Dataset:
    """创建Python Web开发相关的数据集"""
    builder = DatasetBuilder(
        name="python_web_qa",
        version="1.0.0",
        description="Python Web开发代码库评测数据集",
        repo_path=repo_path
    )
    
    # Web框架相关查询
    web_queries = [
        {
            "id": "web_001",
            "query": "如何处理HTTP请求",
            "type": QueryType.CONCEPT_SEARCH,
            "difficulty": DifficultyLevel.MEDIUM,
            "expected_files": ["views.py", "handlers.py", "routes.py", "app.py"]
        },
        {
            "id": "web_002", 
            "query": "用户认证和授权的实现",
            "type": QueryType.FEATURE_IMPLEMENTATION,
            "difficulty": DifficultyLevel.HARD,
            "expected_files": ["auth.py", "authentication.py", "login.py", "middleware.py"]
        },
        {
            "id": "web_003",
            "query": "数据库模型定义",
            "type": QueryType.CLASS_SEARCH,
            "difficulty": DifficultyLevel.EASY,
            "expected_files": ["models.py", "database.py", "schema.py"]
        },
        {
            "id": "web_004",
            "query": "API路由配置",
            "type": QueryType.CONCEPT_SEARCH,
            "difficulty": DifficultyLevel.MEDIUM,
            "expected_files": ["urls.py", "routes.py", "api.py", "endpoints.py"]
        },
        {
            "id": "web_005",
            "query": "表单验证逻辑",
            "type": QueryType.FEATURE_IMPLEMENTATION,
            "difficulty": DifficultyLevel.MEDIUM,
            "expected_files": ["forms.py", "validators.py", "validation.py"]
        },
        {
            "id": "web_006",
            "query": "中间件的实现",
            "type": QueryType.CLASS_SEARCH,
            "difficulty": DifficultyLevel.HARD,
            "expected_files": ["middleware.py", "middlewares.py"]
        },
        {
            "id": "web_007",
            "query": "静态文件处理",
            "type": QueryType.CONCEPT_SEARCH,
            "difficulty": DifficultyLevel.EASY,
            "expected_files": ["static.py", "assets.py", "files.py"]
        },
        {
            "id": "web_008",
            "query": "模板渲染功能",
            "type": QueryType.FEATURE_IMPLEMENTATION,
            "difficulty": DifficultyLevel.MEDIUM,
            "expected_files": ["templates.py", "render.py", "views.py"]
        },
        {
            "id": "web_009",
            "query": "会话管理",
            "type": QueryType.CONCEPT_SEARCH,
            "difficulty": DifficultyLevel.MEDIUM,
            "expected_files": ["session.py", "sessions.py", "auth.py"]
        },
        {
            "id": "web_010",
            "query": "错误处理和异常",
            "type": QueryType.BUG_LOCATION,
            "difficulty": DifficultyLevel.HARD,
            "expected_files": ["errors.py", "exceptions.py", "handlers.py"]
        }
    ]
    
    # 添加查询到数据集
    for query_data in web_queries:
        ground_truth = []
        for file_pattern in query_data["expected_files"]:
            ground_truth.append(
                GroundTruthItem(
                    file_path=f"src/{file_pattern}",  # 假设在src目录下
                    relevance_score=0.8,
                    explanation=f"与{query_data['query']}相关的实现文件"
                )
            )
        
        builder.add_query(
            query_id=query_data["id"],
            query_text=query_data["query"],
            query_type=query_data["type"],
            difficulty=query_data["difficulty"],
            ground_truth=ground_truth
        )
    
    return builder.build()


def create_ml_dataset(repo_path: str) -> Dataset:
    """创建机器学习相关的数据集"""
    builder = DatasetBuilder(
        name="ml_qa",
        version="1.0.0", 
        description="机器学习代码库评测数据集",
        repo_path=repo_path
    )
    
    # 机器学习相关查询
    ml_queries = [
        {
            "id": "ml_001",
            "query": "数据预处理的实现",
            "type": QueryType.FEATURE_IMPLEMENTATION,
            "difficulty": DifficultyLevel.MEDIUM,
            "expected_files": ["preprocessing.py", "data_processing.py", "transform.py"]
        },
        {
            "id": "ml_002",
            "query": "模型训练代码",
            "type": QueryType.CONCEPT_SEARCH,
            "difficulty": DifficultyLevel.HARD,
            "expected_files": ["train.py", "training.py", "model.py"]
        },
        {
            "id": "ml_003",
            "query": "特征工程实现",
            "type": QueryType.FEATURE_IMPLEMENTATION,
            "difficulty": DifficultyLevel.HARD,
            "expected_files": ["features.py", "feature_engineering.py", "extraction.py"]
        },
        {
            "id": "ml_004",
            "query": "模型评估指标",
            "type": QueryType.FUNCTION_SEARCH,
            "difficulty": DifficultyLevel.MEDIUM,
            "expected_files": ["metrics.py", "evaluation.py", "score.py"]
        },
        {
            "id": "ml_005",
            "query": "数据加载器",
            "type": QueryType.CLASS_SEARCH,
            "difficulty": DifficultyLevel.EASY,
            "expected_files": ["dataloader.py", "dataset.py", "data.py"]
        },
        {
            "id": "ml_006",
            "query": "超参数优化",
            "type": QueryType.CONCEPT_SEARCH,
            "difficulty": DifficultyLevel.HARD,
            "expected_files": ["hyperopt.py", "tuning.py", "optimization.py"]
        },
        {
            "id": "ml_007",
            "query": "模型保存和加载",
            "type": QueryType.FEATURE_IMPLEMENTATION,
            "difficulty": DifficultyLevel.MEDIUM,
            "expected_files": ["checkpoint.py", "save_load.py", "persistence.py"]
        },
        {
            "id": "ml_008",
            "query": "损失函数定义",
            "type": QueryType.FUNCTION_SEARCH,
            "difficulty": DifficultyLevel.MEDIUM,
            "expected_files": ["loss.py", "losses.py", "criterion.py"]
        },
        {
            "id": "ml_009",
            "query": "数据增强技术",
            "type": QueryType.CONCEPT_SEARCH,
            "difficulty": DifficultyLevel.MEDIUM,
            "expected_files": ["augmentation.py", "transforms.py", "augment.py"]
        },
        {
            "id": "ml_010",
            "query": "模型推理代码",
            "type": QueryType.FEATURE_IMPLEMENTATION,
            "difficulty": DifficultyLevel.MEDIUM,
            "expected_files": ["inference.py", "predict.py", "prediction.py"]
        }
    ]
    
    # 添加查询到数据集
    for query_data in ml_queries:
        ground_truth = []
        for file_pattern in query_data["expected_files"]:
            ground_truth.append(
                GroundTruthItem(
                    file_path=f"src/{file_pattern}",
                    relevance_score=0.8,
                    explanation=f"与{query_data['query']}相关的实现文件"
                )
            )
        
        builder.add_query(
            query_id=query_data["id"],
            query_text=query_data["query"],
            query_type=query_data["type"],
            difficulty=query_data["difficulty"],
            ground_truth=ground_truth
        )
    
    return builder.build()


def create_general_programming_dataset(repo_path: str) -> Dataset:
    """创建通用编程相关的数据集"""
    builder = DatasetBuilder(
        name="general_programming_qa",
        version="1.0.0",
        description="通用编程概念评测数据集",
        repo_path=repo_path
    )
    
    # 通用编程查询
    general_queries = [
        {
            "id": "gen_001",
            "query": "配置文件读取",
            "type": QueryType.CONCEPT_SEARCH,
            "difficulty": DifficultyLevel.EASY,
            "expected_files": ["config.py", "settings.py", "configuration.py"]
        },
        {
            "id": "gen_002",
            "query": "日志记录实现",
            "type": QueryType.FEATURE_IMPLEMENTATION,
            "difficulty": DifficultyLevel.MEDIUM,
            "expected_files": ["logging.py", "logger.py", "log.py"]
        },
        {
            "id": "gen_003",
            "query": "工具函数定义",
            "type": QueryType.FUNCTION_SEARCH,
            "difficulty": DifficultyLevel.EASY,
            "expected_files": ["utils.py", "helpers.py", "tools.py"]
        },
        {
            "id": "gen_004",
            "query": "单元测试代码",
            "type": QueryType.CONCEPT_SEARCH,
            "difficulty": DifficultyLevel.MEDIUM,
            "expected_files": ["test_*.py", "*_test.py", "tests.py"]
        },
        {
            "id": "gen_005",
            "query": "命令行参数解析",
            "type": QueryType.FEATURE_IMPLEMENTATION,
            "difficulty": DifficultyLevel.MEDIUM,
            "expected_files": ["cli.py", "args.py", "parser.py", "main.py"]
        },
        {
            "id": "gen_006",
            "query": "文件操作工具",
            "type": QueryType.FUNCTION_SEARCH,
            "difficulty": DifficultyLevel.EASY,
            "expected_files": ["file_utils.py", "io.py", "files.py"]
        },
        {
            "id": "gen_007",
            "query": "缓存机制实现",
            "type": QueryType.CONCEPT_SEARCH,
            "difficulty": DifficultyLevel.HARD,
            "expected_files": ["cache.py", "caching.py", "memory.py"]
        },
        {
            "id": "gen_008",
            "query": "异步处理代码",
            "type": QueryType.FEATURE_IMPLEMENTATION,
            "difficulty": DifficultyLevel.HARD,
            "expected_files": ["async.py", "asyncio.py", "concurrent.py"]
        },
        {
            "id": "gen_009",
            "query": "数据结构定义",
            "type": QueryType.CLASS_SEARCH,
            "difficulty": DifficultyLevel.MEDIUM,
            "expected_files": ["structures.py", "data_structures.py", "types.py"]
        },
        {
            "id": "gen_010",
            "query": "性能监控代码",
            "type": QueryType.CONCEPT_SEARCH,
            "difficulty": DifficultyLevel.HARD,
            "expected_files": ["profiler.py", "monitor.py", "performance.py"]
        }
    ]
    
    # 添加查询到数据集
    for query_data in general_queries:
        ground_truth = []
        for file_pattern in query_data["expected_files"]:
            ground_truth.append(
                GroundTruthItem(
                    file_path=file_pattern,
                    relevance_score=0.8,
                    explanation=f"与{query_data['query']}相关的实现文件"
                )
            )
        
        builder.add_query(
            query_id=query_data["id"],
            query_text=query_data["query"],
            query_type=query_data["type"],
            difficulty=query_data["difficulty"],
            ground_truth=ground_truth
        )
    
    return builder.build()


def create_combined_dataset(repo_path: str) -> Dataset:
    """创建综合数据集，包含多个领域的查询"""
    # 创建各个领域的数据集
    web_dataset = create_python_web_dataset(repo_path)
    ml_dataset = create_ml_dataset(repo_path)
    general_dataset = create_general_programming_dataset(repo_path)
    
    # 合并数据集
    builder = DatasetBuilder(
        name="combined_qa",
        version="1.0.0",
        description="综合代码库评测数据集，包含Web开发、机器学习和通用编程",
        repo_path=repo_path
    )
    
    # 添加所有查询
    all_queries = web_dataset.queries + ml_dataset.queries + general_dataset.queries
    for query in all_queries:
        builder.add_query(
            query_id=query.id,
            query_text=query.query,
            query_type=query.query_type,
            difficulty=query.difficulty,
            ground_truth=query.ground_truth,
            language=query.language,
            metadata=query.metadata
        )
    
    return builder.build()


if __name__ == "__main__":
    # 创建示例数据集
    repo_path = "/path/to/sample/repo"
    
    # 创建各种数据集
    web_dataset = create_python_web_dataset(repo_path)
    ml_dataset = create_ml_dataset(repo_path)
    general_dataset = create_general_programming_dataset(repo_path)
    combined_dataset = create_combined_dataset(repo_path)
    
    # 保存数据集
    web_dataset.save("web_dataset.json")
    ml_dataset.save("ml_dataset.json")
    general_dataset.save("general_dataset.json")
    combined_dataset.save("combined_dataset.json")
    
    print("预定义数据集创建完成!")
    print(f"Web数据集: {len(web_dataset.queries)} 个查询")
    print(f"ML数据集: {len(ml_dataset.queries)} 个查询")
    print(f"通用数据集: {len(general_dataset.queries)} 个查询")
    print(f"综合数据集: {len(combined_dataset.queries)} 个查询")
