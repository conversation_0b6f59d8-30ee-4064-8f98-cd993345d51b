#!/usr/bin/env python3
"""
完整的评测运行脚本
支持数据集生成、模型评测、结果分析的完整流程
"""
import os
import sys
import argparse
import asyncio
from pathlib import Path
from typing import Optional, List

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from dataset import Dataset
from dataset_generator import DatasetGenerator
from data_generator import IntelligentDatasetGenerator
from evaluator import Evaluator, EvaluationConfig, run_benchmark
from predefined_datasets import (
    create_python_web_dataset, 
    create_ml_dataset, 
    create_general_programming_dataset,
    create_combined_dataset
)
from utils import setup_logger

logger = setup_logger(__name__)


class BenchmarkRunner:
    """基准测试运行器"""
    
    def __init__(self, args):
        self.args = args
        self.repo_path = args.repo_path
        self.output_dir = args.output_dir
        
        # 创建输出目录
        os.makedirs(self.output_dir, exist_ok=True)
    
    async def run_complete_benchmark(self):
        """运行完整的基准测试流程"""
        logger.info("开始完整基准测试流程")
        
        # Step 1: 生成或加载数据集
        dataset_path = await self._prepare_dataset()
        
        # Step 2: 初始化Pipeline
        pipelines = self._initialize_pipelines()
        
        # Step 3: 运行评测
        results = {}
        for pipeline_name, pipeline in pipelines.items():
            logger.info(f"评测Pipeline: {pipeline_name}")
            
            try:
                result = run_benchmark(
                    pipeline=pipeline,
                    dataset_path=dataset_path,
                    output_dir=os.path.join(self.output_dir, pipeline_name),
                    k_values=self.args.k_values
                )
                results[pipeline_name] = result
                
                # 打印简要结果
                self._print_summary(pipeline_name, result)
                
            except Exception as e:
                logger.error(f"Pipeline {pipeline_name} 评测失败: {e}")
                continue
        
        # Step 4: 生成对比报告
        if len(results) > 1:
            self._generate_comparison_report(results)
        
        logger.info("基准测试完成")
        return results
    
    async def _prepare_dataset(self) -> str:
        """准备数据集"""
        if self.args.dataset_path:
            # 使用现有数据集
            logger.info(f"使用现有数据集: {self.args.dataset_path}")
            return self.args.dataset_path
        
        # 生成新数据集
        dataset_path = os.path.join(self.output_dir, "generated_dataset.json")
        
        if self.args.dataset_type == "intelligent":
            logger.info("生成智能数据集")
            generator = IntelligentDatasetGenerator(
                repo_path=self.repo_path,
                use_llm=self.args.use_llm,
                llm_api_key=self.args.api_key
            )
            dataset = await generator.generate_dataset(self.args.max_queries)
            
        elif self.args.dataset_type == "auto":
            logger.info("生成自动数据集")
            generator = DatasetGenerator(self.repo_path)
            dataset = generator.generate(self.args.max_queries)
            
        elif self.args.dataset_type == "predefined":
            logger.info("生成预定义数据集")
            if self.args.domain == "web":
                dataset = create_python_web_dataset(self.repo_path)
            elif self.args.domain == "ml":
                dataset = create_ml_dataset(self.repo_path)
            elif self.args.domain == "general":
                dataset = create_general_programming_dataset(self.repo_path)
            else:
                dataset = create_combined_dataset(self.repo_path)
        
        else:
            raise ValueError(f"未知的数据集类型: {self.args.dataset_type}")
        
        # 保存数据集
        dataset.save(dataset_path)
        logger.info(f"数据集已保存: {dataset_path}")
        
        return dataset_path
    
    def _initialize_pipelines(self) -> dict:
        """初始化Pipeline"""
        pipelines = {}
        
        # 获取索引路径
        repo_name = os.path.basename(self.repo_path)
        persist_path = self.args.persist_path
        
        index_paths = {
            "bm25": os.path.join(persist_path, "bm25_index_pro", repo_name),
            "embedding": os.path.join(persist_path, "embedding_index_pro", repo_name),
            "graph": os.path.join(persist_path, "graph_index", f"{repo_name}.pkl")
        }
        
        # 导入Pipeline类
        try:
            from pipeline_simple import Pipeline as SimplePipeline
            from pipeline_pro import Pipeline as ProPipeline
            from pipeline_balance import Pipeline as BalancePipeline
            from pipeline_best import Pipeline as BestPipeline
            
            pipeline_classes = {
                "simple": SimplePipeline,
                "pro": ProPipeline,
                "balance": BalancePipeline,
                "best": BestPipeline
            }
            
            # 根据参数选择要评测的Pipeline
            if self.args.pipelines == ["all"]:
                selected_pipelines = list(pipeline_classes.keys())
            else:
                selected_pipelines = self.args.pipelines
            
            for pipeline_name in selected_pipelines:
                if pipeline_name not in pipeline_classes:
                    logger.warning(f"未知的Pipeline类型: {pipeline_name}")
                    continue
                
                try:
                    PipelineClass = pipeline_classes[pipeline_name]
                    
                    if pipeline_name == "simple":
                        pipeline = PipelineClass(
                            bm25_persist_path=index_paths["bm25"],
                            emb_persist_path=index_paths["embedding"]
                        )
                    else:
                        pipeline = PipelineClass(
                            bm25_persist_path=index_paths["bm25"],
                            emb_persist_path=index_paths["embedding"],
                            graph_persist_path=index_paths["graph"]
                        )
                    
                    pipelines[pipeline_name] = pipeline
                    logger.info(f"成功初始化Pipeline: {pipeline_name}")
                    
                except Exception as e:
                    logger.error(f"初始化Pipeline {pipeline_name} 失败: {e}")
                    continue
            
        except ImportError as e:
            logger.error(f"导入Pipeline类失败: {e}")
            raise
        
        if not pipelines:
            raise ValueError("没有成功初始化任何Pipeline")
        
        return pipelines
    
    def _print_summary(self, pipeline_name: str, result):
        """打印评测结果摘要"""
        print(f"\n{'='*50}")
        print(f"Pipeline: {pipeline_name}")
        print(f"{'='*50}")
        
        if hasattr(result, 'summary_report') and result.summary_report:
            summary = result.summary_report.get('summary', {})
            for metric_name, metric_data in summary.items():
                if isinstance(metric_data, dict) and 'value' in metric_data:
                    print(f"{metric_name}: {metric_data['value']:.4f}")
        
        if hasattr(result, 'metadata'):
            metadata = result.metadata
            print(f"总查询数: {metadata.get('total_queries', 0)}")
            print(f"成功查询数: {metadata.get('successful_queries', 0)}")
            print(f"失败查询数: {metadata.get('failed_queries', 0)}")
            print(f"执行时间: {result.execution_time:.2f}秒")
    
    def _generate_comparison_report(self, results: dict):
        """生成对比报告"""
        logger.info("生成Pipeline对比报告")
        
        comparison_data = {
            "pipelines": {},
            "metrics_comparison": {},
            "performance_ranking": {}
        }
        
        # 收集所有Pipeline的指标
        all_metrics = set()
        for pipeline_name, result in results.items():
            if hasattr(result, 'aggregated_metrics'):
                all_metrics.update(result.aggregated_metrics.keys())
        
        # 对比各个指标
        for metric_name in all_metrics:
            comparison_data["metrics_comparison"][metric_name] = {}
            
            for pipeline_name, result in results.items():
                if hasattr(result, 'aggregated_metrics'):
                    metrics = result.aggregated_metrics
                    if metric_name in metrics:
                        value = metrics[metric_name].get('value', 0) if isinstance(metrics[metric_name], dict) else metrics[metric_name]
                        comparison_data["metrics_comparison"][metric_name][pipeline_name] = value
        
        # 保存对比报告
        import json
        comparison_file = os.path.join(self.output_dir, "pipeline_comparison.json")
        with open(comparison_file, 'w', encoding='utf-8') as f:
            json.dump(comparison_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"对比报告已保存: {comparison_file}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="运行代码库检索基准测试")
    
    # 基本配置
    parser.add_argument("--repo-path", required=True, help="代码库路径")
    parser.add_argument("--output-dir", default="./benchmark_results", help="输出目录")
    parser.add_argument("--persist-path", default="./temp", help="索引持久化路径")
    
    # 数据集配置
    parser.add_argument("--dataset-path", help="现有数据集路径")
    parser.add_argument("--dataset-type", choices=["intelligent", "auto", "predefined"], 
                       default="auto", help="数据集生成类型")
    parser.add_argument("--domain", choices=["web", "ml", "general", "combined"], 
                       default="combined", help="预定义数据集领域")
    parser.add_argument("--max-queries", type=int, default=50, help="最大查询数量")
    
    # LLM配置
    parser.add_argument("--use-llm", action="store_true", help="使用LLM生成查询")
    parser.add_argument("--api-key", help="OpenAI API密钥")
    
    # Pipeline配置
    parser.add_argument("--pipelines", nargs="+", 
                       choices=["simple", "pro", "balance", "best", "all"],
                       default=["pro"], help="要评测的Pipeline类型")
    
    # 评测配置
    parser.add_argument("--k-values", nargs="+", type=int, default=[1, 3, 5, 10],
                       help="要计算的k值列表")
    
    args = parser.parse_args()
    
    # 验证参数
    if not os.path.exists(args.repo_path):
        print(f"错误: 代码库路径不存在: {args.repo_path}")
        sys.exit(1)
    
    # 运行基准测试
    runner = BenchmarkRunner(args)
    
    try:
        results = asyncio.run(runner.run_complete_benchmark())
        print(f"\n基准测试完成! 结果保存在: {args.output_dir}")
        
    except KeyboardInterrupt:
        print("\n基准测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n基准测试失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
