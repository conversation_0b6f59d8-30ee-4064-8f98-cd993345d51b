# Codebase QA 评测系统

这是一个完整的代码库问答系统评测框架，支持数据集生成、多维度指标评估和自动化基准测试。

## 功能特性

### 📊 数据集生成
- **自动生成**: 基于代码库结构自动生成查询-答案对
- **智能生成**: 使用LLM生成高质量的自然语言查询
- **预定义数据集**: 针对Web开发、机器学习等领域的专业数据集
- **多种查询类型**: 函数搜索、类搜索、概念搜索、API使用等

### 🎯 评测指标
- **精确率/召回率**: Precision@K, Recall@K, F1@K
- **排序质量**: NDCG@K, MAP, MRR
- **命中率**: Hit Rate@K
- **覆盖率**: Coverage
- **多维度分析**: 按查询类型、难度级别分析

### 🚀 Pipeline评测
- **多Pipeline支持**: Simple, Pro, Balance, Best
- **自动化评测**: 一键运行完整评测流程
- **对比分析**: 多Pipeline性能对比
- **详细报告**: 生成详细的评测报告

## 快速开始

### 1. 安装依赖

```bash
# 基础依赖
pip install -r requirements.txt

# 可选: LLM支持
pip install openai
```

### 2. 准备索引

确保已经为你的代码库构建了索引：

```bash
# 设置环境变量
export REPO_PATH="/path/to/your/repository"
export PERSIST_PATH="./temp"

# 构建索引
bash scripts/pro/build_index.sh
```

### 3. 运行基准测试

#### 基础评测
```bash
python benchmark/run_evaluation.py \
    --repo-path /path/to/your/repo \
    --output-dir ./results \
    --pipelines pro
```

#### 完整评测（所有Pipeline）
```bash
python benchmark/run_evaluation.py \
    --repo-path /path/to/your/repo \
    --output-dir ./results \
    --pipelines all \
    --max-queries 100
```

#### 使用LLM生成高质量数据集
```bash
python benchmark/run_evaluation.py \
    --repo-path /path/to/your/repo \
    --dataset-type intelligent \
    --use-llm \
    --api-key your_openai_api_key \
    --max-queries 50
```

## 数据集类型

### 自动生成数据集
基于代码库结构自动生成：
```bash
python benchmark/dataset_generator.py \
    --repo-path /path/to/repo \
    --output generated_dataset.json \
    --max-queries 100
```

### 智能生成数据集
使用LLM生成高质量查询：
```bash
python benchmark/data_generator.py \
    --repo-path /path/to/repo \
    --output intelligent_dataset.json \
    --use-llm \
    --api-key your_api_key
```

### 预定义数据集
针对特定领域的专业数据集：
```python
from benchmark import create_python_web_dataset, create_ml_dataset

# Web开发数据集
web_dataset = create_python_web_dataset("/path/to/repo")
web_dataset.save("web_dataset.json")

# 机器学习数据集
ml_dataset = create_ml_dataset("/path/to/repo")
ml_dataset.save("ml_dataset.json")
```

## 评测指标说明

### 基础指标
- **Precision@K**: 前K个结果中相关结果的比例
- **Recall@K**: 前K个结果覆盖的相关结果比例
- **F1@K**: Precision和Recall的调和平均

### 排序指标
- **NDCG@K**: 考虑位置权重的排序质量指标
- **MAP**: 平均精确率，综合考虑所有相关结果
- **MRR**: 平均倒数排名，关注第一个相关结果的位置

### 覆盖指标
- **Hit Rate@K**: 前K个结果中是否包含相关结果
- **Coverage**: 所有相关文档中被检索到的比例

## 使用示例

### 1. 单独运行评测

```python
from benchmark import run_benchmark
from pipeline_pro import Pipeline

# 初始化Pipeline
pipeline = Pipeline(
    bm25_persist_path="./temp/bm25_index/repo",
    emb_persist_path="./temp/embedding_index/repo"
)

# 运行评测
result = run_benchmark(
    pipeline=pipeline,
    dataset_path="dataset.json",
    output_dir="./results"
)

print(f"Precision@5: {result.summary_report['summary']['precision@5']['value']}")
```

### 2. 自定义评测

```python
from benchmark import Evaluator, EvaluationConfig, Dataset

# 加载数据集
dataset = Dataset.load("dataset.json")

# 配置评测
config = EvaluationConfig(
    dataset_path="dataset.json",
    output_dir="./results",
    k_values=[1, 3, 5, 10, 20]
)

# 运行评测
evaluator = Evaluator(config)
evaluator.set_pipeline(pipeline)
result = evaluator.run_evaluation()
```

### 3. 生成对比报告

```python
# 评测多个Pipeline
pipelines = {
    "simple": SimplePipeline(...),
    "pro": ProPipeline(...),
    "best": BestPipeline(...)
}

results = {}
for name, pipeline in pipelines.items():
    results[name] = run_benchmark(pipeline, "dataset.json", f"./results/{name}")

# 生成对比报告
from benchmark.run_evaluation import BenchmarkRunner
runner = BenchmarkRunner(args)
runner._generate_comparison_report(results)
```

## 输出文件说明

评测完成后会生成以下文件：

```
results/
├── evaluation_result_20231201_120000.json    # 主要评测结果
├── detailed_results_20231201_120000.json     # 详细查询结果
├── failed_queries_20231201_120000.json       # 失败查询列表
└── pipeline_comparison.json                   # Pipeline对比报告
```

### 评测结果格式

```json
{
  "aggregated_metrics": {
    "precision@5": {"value": 0.8, "details": {...}},
    "recall@5": {"value": 0.6, "details": {...}},
    "ndcg@5": {"value": 0.75, "details": {...}}
  },
  "summary_report": {
    "summary": {...},
    "performance_analysis": {...}
  },
  "metadata": {
    "total_queries": 100,
    "successful_queries": 95,
    "failed_queries": 5,
    "execution_time": 120.5
  }
}
```

## 高级功能

### 1. 自定义指标

```python
from benchmark.metrics import MetricsCalculator

class CustomMetricsCalculator(MetricsCalculator):
    def calculate_custom_metric(self, retrieved_results, ground_truth):
        # 实现自定义指标
        pass
```

### 2. 自定义数据集

```python
from benchmark import DatasetBuilder, QueryType, DifficultyLevel

builder = DatasetBuilder("custom_dataset", "1.0.0", "自定义数据集", "/path/to/repo")

builder.add_query(
    query_id="custom_001",
    query_text="自定义查询",
    query_type=QueryType.CONCEPT_SEARCH,
    difficulty=DifficultyLevel.MEDIUM,
    ground_truth=[...]
)

dataset = builder.build()
```

### 3. 批量评测

```bash
# 评测多个代码库
for repo in repo1 repo2 repo3; do
    python benchmark/run_evaluation.py \
        --repo-path /path/to/$repo \
        --output-dir ./results/$repo \
        --pipelines all
done
```

## 故障排除

### 常见问题

1. **"索引文件不存在"**
   - 确保已构建相应的索引文件
   - 检查PERSIST_PATH路径设置

2. **"LLM API调用失败"**
   - 检查API密钥设置
   - 确认网络连接正常

3. **"内存不足"**
   - 减少max_queries数量
   - 使用更小的k_values

4. **"Pipeline初始化失败"**
   - 检查索引文件路径
   - 验证依赖库安装

### 调试模式

```bash
python benchmark/run_evaluation.py \
    --repo-path /path/to/repo \
    --output-dir ./debug_results \
    --max-queries 10 \
    --pipelines simple
```

## 贡献指南

欢迎贡献新的评测指标、数据集类型或Pipeline实现！

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

## 许可证

MIT License
