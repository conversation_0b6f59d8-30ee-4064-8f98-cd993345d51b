"""
自动生成评测数据集
基于代码库内容自动生成查询-答案对
"""
import os
import re
import ast
import random
from typing import List, Dict, Any, Optional, Set, Tuple
from pathlib import Path

from dataset import (
    Dataset, DatasetBuilder, QueryItem, GroundTruthItem,
    QueryType, DifficultyLevel, QUERY_TEMPLATES
)
from utils import setup_logger

logger = setup_logger(__name__)


class CodeAnalyzer:
    """代码分析器"""
    
    def __init__(self, repo_path: str):
        self.repo_path = repo_path
        self.python_files: List[str] = []
        self.functions: Dict[str, List[Dict]] = {}  # file_path -> [function_info]
        self.classes: Dict[str, List[Dict]] = {}   # file_path -> [class_info]
        self.imports: Dict[str, List[str]] = {}    # file_path -> [import_statements]
        
    def scan_repository(self):
        """扫描代码库"""
        logger.info(f"扫描代码库: {self.repo_path}")
        
        for root, dirs, files in os.walk(self.repo_path):
            # 跳过常见的忽略目录
            dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['__pycache__', 'node_modules']]
            
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    rel_path = os.path.relpath(file_path, self.repo_path)
                    self.python_files.append(rel_path)
        
        logger.info(f"找到 {len(self.python_files)} 个Python文件")
        
        # 分析每个文件
        for file_path in self.python_files:
            self._analyze_file(file_path)
    
    def _analyze_file(self, rel_file_path: str):
        """分析单个文件"""
        full_path = os.path.join(self.repo_path, rel_file_path)
        
        try:
            with open(full_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 解析AST
            tree = ast.parse(content)
            
            functions = []
            classes = []
            imports = []
            
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    func_info = {
                        'name': node.name,
                        'lineno': node.lineno,
                        'end_lineno': getattr(node, 'end_lineno', node.lineno),
                        'docstring': ast.get_docstring(node),
                        'args': [arg.arg for arg in node.args.args],
                        'is_method': False
                    }
                    functions.append(func_info)
                
                elif isinstance(node, ast.ClassDef):
                    class_info = {
                        'name': node.name,
                        'lineno': node.lineno,
                        'end_lineno': getattr(node, 'end_lineno', node.lineno),
                        'docstring': ast.get_docstring(node),
                        'methods': [],
                        'bases': [base.id if isinstance(base, ast.Name) else str(base) for base in node.bases]
                    }
                    
                    # 获取类中的方法
                    for item in node.body:
                        if isinstance(item, ast.FunctionDef):
                            method_info = {
                                'name': item.name,
                                'lineno': item.lineno,
                                'end_lineno': getattr(item, 'end_lineno', item.lineno),
                                'docstring': ast.get_docstring(item),
                                'args': [arg.arg for arg in item.args.args],
                                'is_method': True
                            }
                            class_info['methods'].append(method_info)
                            functions.append(method_info)  # 也添加到函数列表中
                    
                    classes.append(class_info)
                
                elif isinstance(node, (ast.Import, ast.ImportFrom)):
                    if isinstance(node, ast.Import):
                        for alias in node.names:
                            imports.append(f"import {alias.name}")
                    else:
                        module = node.module or ""
                        for alias in node.names:
                            imports.append(f"from {module} import {alias.name}")
            
            self.functions[rel_file_path] = functions
            self.classes[rel_file_path] = classes
            self.imports[rel_file_path] = imports
            
        except Exception as e:
            logger.warning(f"分析文件 {rel_file_path} 失败: {e}")
    
    def get_all_functions(self) -> List[Tuple[str, Dict]]:
        """获取所有函数信息"""
        all_functions = []
        for file_path, functions in self.functions.items():
            for func in functions:
                all_functions.append((file_path, func))
        return all_functions
    
    def get_all_classes(self) -> List[Tuple[str, Dict]]:
        """获取所有类信息"""
        all_classes = []
        for file_path, classes in self.classes.items():
            for cls in classes:
                all_classes.append((file_path, cls))
        return all_classes


class DatasetGenerator:
    """数据集生成器"""
    
    def __init__(self, repo_path: str, dataset_name: str = "auto_generated"):
        self.repo_path = repo_path
        self.dataset_name = dataset_name
        self.analyzer = CodeAnalyzer(repo_path)
        self.builder = DatasetBuilder(
            name=dataset_name,
            version="1.0.0",
            description=f"自动生成的代码库评测数据集 - {os.path.basename(repo_path)}",
            repo_path=repo_path
        )
    
    def generate(self, max_queries: int = 100) -> Dataset:
        """生成数据集"""
        logger.info("开始生成评测数据集")
        
        # 扫描代码库
        self.analyzer.scan_repository()
        
        # 生成不同类型的查询
        query_count = 0
        
        # 1. 函数搜索查询
        function_queries = min(max_queries // 4, 25)
        query_count += self._generate_function_queries(function_queries)
        
        # 2. 类搜索查询
        class_queries = min(max_queries // 4, 25)
        query_count += self._generate_class_queries(class_queries)
        
        # 3. 概念搜索查询
        concept_queries = min(max_queries // 4, 25)
        query_count += self._generate_concept_queries(concept_queries)
        
        # 4. API使用查询
        api_queries = min(max_queries - query_count, 25)
        query_count += self._generate_api_queries(api_queries)
        
        logger.info(f"生成了 {query_count} 个查询")
        
        return self.builder.build()
    
    def _generate_function_queries(self, count: int) -> int:
        """生成函数搜索查询"""
        all_functions = self.analyzer.get_all_functions()
        if not all_functions:
            return 0
        
        selected_functions = random.sample(all_functions, min(count, len(all_functions)))
        generated = 0
        
        for file_path, func_info in selected_functions:
            func_name = func_info['name']
            
            # 跳过私有函数和特殊方法
            if func_name.startswith('_'):
                continue
            
            # 选择查询模板
            template = random.choice(QUERY_TEMPLATES[QueryType.FUNCTION_SEARCH])
            query_text = template.format(function_name=func_name)
            
            # 确定难度
            difficulty = DifficultyLevel.EASY if len(func_name) > 5 else DifficultyLevel.MEDIUM
            
            # 创建标准答案
            ground_truth = [
                GroundTruthItem(
                    file_path=file_path,
                    start_line=func_info['lineno'],
                    end_line=func_info.get('end_lineno', func_info['lineno']),
                    relevance_score=1.0,
                    explanation=f"函数 {func_name} 的定义"
                )
            ]
            
            self.builder.add_query(
                query_id=f"func_{generated:03d}",
                query_text=query_text,
                query_type=QueryType.FUNCTION_SEARCH,
                difficulty=difficulty,
                ground_truth=ground_truth,
                metadata={"function_name": func_name, "file_path": file_path}
            )
            
            generated += 1
        
        return generated
    
    def _generate_class_queries(self, count: int) -> int:
        """生成类搜索查询"""
        all_classes = self.analyzer.get_all_classes()
        if not all_classes:
            return 0
        
        selected_classes = random.sample(all_classes, min(count, len(all_classes)))
        generated = 0
        
        for file_path, class_info in selected_classes:
            class_name = class_info['name']
            
            # 跳过私有类
            if class_name.startswith('_'):
                continue
            
            # 选择查询模板
            template = random.choice(QUERY_TEMPLATES[QueryType.CLASS_SEARCH])
            query_text = template.format(class_name=class_name)
            
            # 确定难度
            difficulty = DifficultyLevel.EASY if len(class_info['methods']) < 5 else DifficultyLevel.MEDIUM
            
            # 创建标准答案
            ground_truth = [
                GroundTruthItem(
                    file_path=file_path,
                    start_line=class_info['lineno'],
                    end_line=class_info.get('end_lineno', class_info['lineno']),
                    relevance_score=1.0,
                    explanation=f"类 {class_name} 的定义"
                )
            ]
            
            self.builder.add_query(
                query_id=f"class_{generated:03d}",
                query_text=query_text,
                query_type=QueryType.CLASS_SEARCH,
                difficulty=difficulty,
                ground_truth=ground_truth,
                metadata={"class_name": class_name, "file_path": file_path}
            )
            
            generated += 1
        
        return generated
    
    def _generate_concept_queries(self, count: int) -> int:
        """生成概念搜索查询"""
        # 基于文件名和目录结构生成概念查询
        concepts = set()
        
        for file_path in self.analyzer.python_files:
            # 从文件路径提取概念
            parts = file_path.replace('.py', '').split('/')
            for part in parts:
                if part and not part.startswith('_') and len(part) > 3:
                    concepts.add(part)
        
        # 常见的编程概念
        common_concepts = [
            "配置", "日志", "数据库", "认证", "授权", "缓存", "队列",
            "API", "路由", "中间件", "模型", "视图", "控制器",
            "工具", "帮助", "测试", "错误处理", "异常"
        ]
        concepts.update(common_concepts)
        
        selected_concepts = random.sample(list(concepts), min(count, len(concepts)))
        generated = 0
        
        for concept in selected_concepts:
            # 查找相关文件
            related_files = []
            for file_path in self.analyzer.python_files:
                if concept.lower() in file_path.lower():
                    related_files.append(file_path)
            
            if not related_files:
                continue
            
            # 选择查询模板
            template = random.choice(QUERY_TEMPLATES[QueryType.CONCEPT_SEARCH])
            query_text = template.format(concept=concept)
            
            # 创建标准答案
            ground_truth = []
            for file_path in related_files[:3]:  # 最多3个相关文件
                ground_truth.append(
                    GroundTruthItem(
                        file_path=file_path,
                        relevance_score=0.8,
                        explanation=f"与概念 {concept} 相关的文件"
                    )
                )
            
            if ground_truth:
                self.builder.add_query(
                    query_id=f"concept_{generated:03d}",
                    query_text=query_text,
                    query_type=QueryType.CONCEPT_SEARCH,
                    difficulty=DifficultyLevel.MEDIUM,
                    ground_truth=ground_truth,
                    metadata={"concept": concept}
                )
                
                generated += 1
        
        return generated
    
    def _generate_api_queries(self, count: int) -> int:
        """生成API使用查询"""
        # 基于导入语句生成API查询
        api_names = set()
        
        for file_path, imports in self.analyzer.imports.items():
            for import_stmt in imports:
                # 提取API名称
                if 'import' in import_stmt:
                    parts = import_stmt.split()
                    if len(parts) >= 2:
                        api_name = parts[-1]
                        if not api_name.startswith('_') and len(api_name) > 2:
                            api_names.add(api_name)
        
        selected_apis = random.sample(list(api_names), min(count, len(api_names)))
        generated = 0
        
        for api_name in selected_apis:
            # 查找使用该API的文件
            related_files = []
            for file_path, imports in self.analyzer.imports.items():
                for import_stmt in imports:
                    if api_name in import_stmt:
                        related_files.append(file_path)
                        break
            
            if not related_files:
                continue
            
            # 选择查询模板
            template = random.choice(QUERY_TEMPLATES[QueryType.API_USAGE])
            query_text = template.format(api_name=api_name)
            
            # 创建标准答案
            ground_truth = []
            for file_path in related_files[:2]:  # 最多2个相关文件
                ground_truth.append(
                    GroundTruthItem(
                        file_path=file_path,
                        relevance_score=0.7,
                        explanation=f"使用 {api_name} API 的文件"
                    )
                )
            
            if ground_truth:
                self.builder.add_query(
                    query_id=f"api_{generated:03d}",
                    query_text=query_text,
                    query_type=QueryType.API_USAGE,
                    difficulty=DifficultyLevel.MEDIUM,
                    ground_truth=ground_truth,
                    metadata={"api_name": api_name}
                )
                
                generated += 1
        
        return generated


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="生成评测数据集")
    parser.add_argument("--repo-path", required=True, help="代码库路径")
    parser.add_argument("--output", default="generated_dataset.json", help="输出文件路径")
    parser.add_argument("--max-queries", type=int, default=100, help="最大查询数量")
    parser.add_argument("--dataset-name", default="auto_generated", help="数据集名称")
    
    args = parser.parse_args()
    
    # 生成数据集
    generator = DatasetGenerator(args.repo_path, args.dataset_name)
    dataset = generator.generate(args.max_queries)
    
    # 保存数据集
    dataset.save(args.output)
    
    # 打印统计信息
    stats = dataset.get_statistics()
    print("数据集生成完成!")
    print(f"输出文件: {args.output}")
    print("统计信息:")
    for key, value in stats.items():
        print(f"  {key}: {value}")


if __name__ == "__main__":
    main()
