"""
评测指标计算
实现各种检索质量评估指标
"""
import math
from typing import List, Dict, Any, Set, Tuple, Optional
from dataclasses import dataclass
from enum import Enum

from .dataset import GroundTruthItem


class MetricType(str, Enum):
    """指标类型"""
    PRECISION = "precision"
    RECALL = "recall"
    F1_SCORE = "f1_score"
    MAP = "map"  # Mean Average Precision
    NDCG = "ndcg"  # Normalized Discounted Cumulative Gain
    MRR = "mrr"  # Mean Reciprocal Rank
    HIT_RATE = "hit_rate"
    COVERAGE = "coverage"


@dataclass
class RetrievalResult:
    """检索结果"""
    file_path: str
    score: float
    rank: int
    text: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class MetricResult:
    """指标计算结果"""
    metric_type: MetricType
    value: float
    details: Optional[Dict[str, Any]] = None


class MetricsCalculator:
    """指标计算器"""
    
    def __init__(self):
        self.epsilon = 1e-10  # 避免除零错误
    
    def calculate_precision_recall(
        self,
        retrieved_results: List[RetrievalResult],
        ground_truth: List[GroundTruthItem],
        k: Optional[int] = None
    ) -> Tuple[float, float]:
        """
        计算Precision和Recall
        
        Args:
            retrieved_results: 检索结果列表
            ground_truth: 标准答案列表
            k: 计算前k个结果，None表示全部
            
        Returns:
            (precision, recall)
        """
        if k is not None:
            retrieved_results = retrieved_results[:k]
        
        # 获取检索到的文件路径集合
        retrieved_files = {result.file_path for result in retrieved_results}
        
        # 获取标准答案文件路径集合
        relevant_files = {gt.file_path for gt in ground_truth}
        
        # 计算交集
        relevant_retrieved = retrieved_files.intersection(relevant_files)
        
        # 计算Precision和Recall
        precision = len(relevant_retrieved) / (len(retrieved_files) + self.epsilon)
        recall = len(relevant_retrieved) / (len(relevant_files) + self.epsilon)
        
        return precision, recall
    
    def calculate_f1_score(self, precision: float, recall: float) -> float:
        """计算F1分数"""
        if precision + recall == 0:
            return 0.0
        return 2 * precision * recall / (precision + recall)
    
    def calculate_average_precision(
        self,
        retrieved_results: List[RetrievalResult],
        ground_truth: List[GroundTruthItem]
    ) -> float:
        """
        计算Average Precision (AP)
        
        Args:
            retrieved_results: 检索结果列表（按相关性排序）
            ground_truth: 标准答案列表
            
        Returns:
            Average Precision值
        """
        relevant_files = {gt.file_path for gt in ground_truth}
        
        if not relevant_files:
            return 0.0
        
        precision_sum = 0.0
        relevant_count = 0
        
        for i, result in enumerate(retrieved_results):
            if result.file_path in relevant_files:
                relevant_count += 1
                precision_at_i = relevant_count / (i + 1)
                precision_sum += precision_at_i
        
        return precision_sum / len(relevant_files) if relevant_files else 0.0
    
    def calculate_ndcg(
        self,
        retrieved_results: List[RetrievalResult],
        ground_truth: List[GroundTruthItem],
        k: Optional[int] = None
    ) -> float:
        """
        计算Normalized Discounted Cumulative Gain (NDCG)
        
        Args:
            retrieved_results: 检索结果列表
            ground_truth: 标准答案列表
            k: 计算前k个结果
            
        Returns:
            NDCG值
        """
        if k is not None:
            retrieved_results = retrieved_results[:k]
        
        # 创建相关性映射
        relevance_map = {gt.file_path: gt.relevance_score for gt in ground_truth}
        
        # 计算DCG
        dcg = 0.0
        for i, result in enumerate(retrieved_results):
            relevance = relevance_map.get(result.file_path, 0.0)
            if i == 0:
                dcg += relevance
            else:
                dcg += relevance / math.log2(i + 1)
        
        # 计算IDCG (理想DCG)
        ideal_relevances = sorted([gt.relevance_score for gt in ground_truth], reverse=True)
        if k is not None:
            ideal_relevances = ideal_relevances[:k]
        
        idcg = 0.0
        for i, relevance in enumerate(ideal_relevances):
            if i == 0:
                idcg += relevance
            else:
                idcg += relevance / math.log2(i + 1)
        
        return dcg / idcg if idcg > 0 else 0.0
    
    def calculate_mrr(
        self,
        retrieved_results: List[RetrievalResult],
        ground_truth: List[GroundTruthItem]
    ) -> float:
        """
        计算Mean Reciprocal Rank (MRR)
        
        Args:
            retrieved_results: 检索结果列表
            ground_truth: 标准答案列表
            
        Returns:
            MRR值
        """
        relevant_files = {gt.file_path for gt in ground_truth}
        
        for i, result in enumerate(retrieved_results):
            if result.file_path in relevant_files:
                return 1.0 / (i + 1)
        
        return 0.0
    
    def calculate_hit_rate(
        self,
        retrieved_results: List[RetrievalResult],
        ground_truth: List[GroundTruthItem],
        k: Optional[int] = None
    ) -> float:
        """
        计算Hit Rate@K
        
        Args:
            retrieved_results: 检索结果列表
            ground_truth: 标准答案列表
            k: 前k个结果
            
        Returns:
            Hit Rate值
        """
        if k is not None:
            retrieved_results = retrieved_results[:k]
        
        retrieved_files = {result.file_path for result in retrieved_results}
        relevant_files = {gt.file_path for gt in ground_truth}
        
        return 1.0 if retrieved_files.intersection(relevant_files) else 0.0
    
    def calculate_coverage(
        self,
        all_retrieved_results: List[List[RetrievalResult]],
        all_ground_truth: List[List[GroundTruthItem]]
    ) -> float:
        """
        计算覆盖率 - 所有相关文档中被检索到的比例
        
        Args:
            all_retrieved_results: 所有查询的检索结果
            all_ground_truth: 所有查询的标准答案
            
        Returns:
            覆盖率
        """
        all_relevant_files = set()
        all_retrieved_files = set()
        
        for ground_truth in all_ground_truth:
            all_relevant_files.update(gt.file_path for gt in ground_truth)
        
        for retrieved_results in all_retrieved_results:
            all_retrieved_files.update(result.file_path for result in retrieved_results)
        
        covered_files = all_relevant_files.intersection(all_retrieved_files)
        
        return len(covered_files) / len(all_relevant_files) if all_relevant_files else 0.0
    
    def calculate_all_metrics(
        self,
        retrieved_results: List[RetrievalResult],
        ground_truth: List[GroundTruthItem],
        k_values: List[int] = [1, 3, 5, 10]
    ) -> Dict[str, MetricResult]:
        """
        计算所有指标
        
        Args:
            retrieved_results: 检索结果列表
            ground_truth: 标准答案列表
            k_values: 要计算的k值列表
            
        Returns:
            指标结果字典
        """
        results = {}
        
        # 计算各种k值下的指标
        for k in k_values:
            precision, recall = self.calculate_precision_recall(retrieved_results, ground_truth, k)
            f1 = self.calculate_f1_score(precision, recall)
            ndcg = self.calculate_ndcg(retrieved_results, ground_truth, k)
            hit_rate = self.calculate_hit_rate(retrieved_results, ground_truth, k)
            
            results[f"precision@{k}"] = MetricResult(MetricType.PRECISION, precision)
            results[f"recall@{k}"] = MetricResult(MetricType.RECALL, recall)
            results[f"f1@{k}"] = MetricResult(MetricType.F1_SCORE, f1)
            results[f"ndcg@{k}"] = MetricResult(MetricType.NDCG, ndcg)
            results[f"hit_rate@{k}"] = MetricResult(MetricType.HIT_RATE, hit_rate)
        
        # 计算不依赖k的指标
        ap = self.calculate_average_precision(retrieved_results, ground_truth)
        mrr = self.calculate_mrr(retrieved_results, ground_truth)
        
        results["map"] = MetricResult(MetricType.MAP, ap)
        results["mrr"] = MetricResult(MetricType.MRR, mrr)
        
        return results


class MetricsAggregator:
    """指标聚合器"""
    
    def __init__(self):
        self.calculator = MetricsCalculator()
    
    def aggregate_metrics(
        self,
        all_results: List[Dict[str, MetricResult]]
    ) -> Dict[str, MetricResult]:
        """
        聚合多个查询的指标结果
        
        Args:
            all_results: 所有查询的指标结果列表
            
        Returns:
            聚合后的指标结果
        """
        if not all_results:
            return {}
        
        # 获取所有指标名称
        metric_names = set()
        for result in all_results:
            metric_names.update(result.keys())
        
        aggregated = {}
        
        for metric_name in metric_names:
            values = []
            for result in all_results:
                if metric_name in result:
                    values.append(result[metric_name].value)
            
            if values:
                mean_value = sum(values) / len(values)
                aggregated[metric_name] = MetricResult(
                    metric_type=all_results[0][metric_name].metric_type,
                    value=mean_value,
                    details={
                        "count": len(values),
                        "min": min(values),
                        "max": max(values),
                        "std": self._calculate_std(values, mean_value)
                    }
                )
        
        return aggregated
    
    def _calculate_std(self, values: List[float], mean: float) -> float:
        """计算标准差"""
        if len(values) <= 1:
            return 0.0
        
        variance = sum((x - mean) ** 2 for x in values) / (len(values) - 1)
        return math.sqrt(variance)
    
    def create_summary_report(
        self,
        aggregated_metrics: Dict[str, MetricResult]
    ) -> Dict[str, Any]:
        """
        创建汇总报告
        
        Args:
            aggregated_metrics: 聚合后的指标
            
        Returns:
            汇总报告
        """
        report = {
            "summary": {},
            "detailed_metrics": {},
            "performance_analysis": {}
        }
        
        # 提取关键指标
        key_metrics = ["precision@5", "recall@5", "f1@5", "ndcg@5", "map", "mrr"]
        
        for metric_name in key_metrics:
            if metric_name in aggregated_metrics:
                metric = aggregated_metrics[metric_name]
                report["summary"][metric_name] = {
                    "value": round(metric.value, 4),
                    "details": metric.details
                }
        
        # 详细指标
        for metric_name, metric in aggregated_metrics.items():
            report["detailed_metrics"][metric_name] = {
                "type": metric.metric_type.value,
                "value": round(metric.value, 4),
                "details": metric.details
            }
        
        # 性能分析
        if "precision@5" in aggregated_metrics and "recall@5" in aggregated_metrics:
            precision = aggregated_metrics["precision@5"].value
            recall = aggregated_metrics["recall@5"].value
            
            if precision > 0.8:
                precision_level = "excellent"
            elif precision > 0.6:
                precision_level = "good"
            elif precision > 0.4:
                precision_level = "fair"
            else:
                precision_level = "poor"
            
            if recall > 0.8:
                recall_level = "excellent"
            elif recall > 0.6:
                recall_level = "good"
            elif recall > 0.4:
                recall_level = "fair"
            else:
                recall_level = "poor"
            
            report["performance_analysis"] = {
                "precision_level": precision_level,
                "recall_level": recall_level,
                "overall_assessment": f"Precision: {precision_level}, Recall: {recall_level}"
            }
        
        return report


def convert_pipeline_results_to_retrieval_results(
    pipeline_results: List[Dict[str, Any]]
) -> List[RetrievalResult]:
    """
    将pipeline结果转换为RetrievalResult格式

    Args:
        pipeline_results: Pipeline返回的结果列表

    Returns:
        RetrievalResult列表
    """
    retrieval_results = []

    for i, result in enumerate(pipeline_results):
        retrieval_result = RetrievalResult(
            file_path=result.get("file_path", ""),
            score=result.get("score", 0.0),
            rank=i + 1,
            text=result.get("text", ""),
            metadata=result.get("metadata", {})
        )
        retrieval_results.append(retrieval_result)

    return retrieval_results


if __name__ == "__main__":
    # 测试指标计算
    calculator = MetricsCalculator()
    
    # 模拟检索结果
    retrieved_results = [
        RetrievalResult("file1.py", 0.9, 1),
        RetrievalResult("file2.py", 0.8, 2),
        RetrievalResult("file3.py", 0.7, 3),
        RetrievalResult("file4.py", 0.6, 4),
        RetrievalResult("file5.py", 0.5, 5),
    ]
    
    # 模拟标准答案
    ground_truth = [
        GroundTruthItem(file_path="file1.py", relevance_score=1.0),
        GroundTruthItem(file_path="file3.py", relevance_score=0.8),
        GroundTruthItem(file_path="file6.py", relevance_score=0.6),
    ]
    
    # 计算指标
    metrics = calculator.calculate_all_metrics(retrieved_results, ground_truth)
    
    print("指标计算结果:")
    for name, result in metrics.items():
        print(f"{name}: {result.value:.4f}")
    
    # 测试聚合
    aggregator = MetricsAggregator()
    aggregated = aggregator.aggregate_metrics([metrics])
    report = aggregator.create_summary_report(aggregated)
    
    print("\n汇总报告:")
    import json
    print(json.dumps(report, indent=2, ensure_ascii=False))
