"""
Balance Pipeline - 平衡版本
在效果和速度之间取得平衡，根据需求裁剪流程
"""
from typing import List, Dict, Any, Optional
from retriever import BM25RetrieverSimple, EmbeddingRetrieverPro
from pipeline_base import BasePipeline
from utils import setup_logger, Config

logger = setup_logger(__name__)


class Pipeline(BasePipeline):
    """Balance Pipeline实现 - 平衡效果和性能"""

    def __init__(
        self,
        bm25_persist_path: str = None,
        emb_persist_path: str = None,
        enable_rerank: bool = True,
        config: Optional[Config] = None
    ) -> None:
        # 自定义配置以平衡性能
        if config is None:
            config = Config()
            # 调整配置以平衡性能
            config.retrieval.bm25_top_k = 8
            config.retrieval.embedding_top_k = 8
            config.retrieval.enable_rerank = enable_rerank
            config.retrieval.rerank_top_n = 8

        super().__init__(
            bm25_persist_path=bm25_persist_path,
            emb_persist_path=emb_persist_path,
            config=config
        )

    def _create_bm25_retriever(self, persist_path: str):
        """创建BM25检索器 - 使用Simple版本以提高速度"""
        return BM25RetrieverSimple(persist_path)

    def _create_embedding_retriever(self, persist_path: str):
        """创建Embedding检索器 - 使用Pro版本以提高效果"""
        return EmbeddingRetrieverPro(persist_path)

    def run(self, query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """
        运行Balance Pipeline

        Args:
            query: 查询字符串
            top_k: 返回结果数量

        Returns:
            检索结果列表
        """
        logger.info(f"开始执行Balance Pipeline查询: '{query}'")

        # Step 1: 查询增强
        augmented_query = self.query_augment.augment(query)
        logger.debug(f"查询增强: '{query}' -> '{augmented_query}'")

        # Step 2: 检索 - 只使用BM25和Embedding，跳过Graph以提高速度
        bm25_results = self._retrieve_bm25(augmented_query)
        embedding_results = self._retrieve_embedding(augmented_query)

        # Step 3: 合并结果
        merged_results = self.result_merger.merge_results(
            bm25_results=bm25_results,
            embedding_results=embedding_results
        )

        # Step 4: 条件性重排序 - 根据配置决定是否重排序
        if self.config.retrieval.enable_rerank:
            final_results = self._rerank_results(augmented_query, merged_results)
        else:
            final_results = merged_results

        # Step 5: 返回结果
        final_results = final_results[:top_k]
        result_dicts = [result.model_dump() for result in final_results]

        logger.info(f"Balance Pipeline完成，返回 {len(result_dicts)} 条结果")
        return result_dicts


if __name__ == "__main__":
    # 测试代码
    import os

    repo_name = "test_repo"
    persist_path = "./temp"

    bm25_persist_path = os.path.join(persist_path, "bm25_index_simple", repo_name)
    emb_persist_path = os.path.join(persist_path, "embedding_index_pro", repo_name)

    # 创建pipeline实例
    pipeline = Pipeline(
        bm25_persist_path=bm25_persist_path,
        emb_persist_path=emb_persist_path,
        enable_rerank=True  # 可以动态控制是否启用重排序
    )

    # 执行查询
    query = "如何实现代码检索功能"
    results = pipeline.run(query, top_k=5)

    from pprint import pprint
    pprint(results)