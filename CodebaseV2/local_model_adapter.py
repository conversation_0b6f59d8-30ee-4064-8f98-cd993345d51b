"""
本地模型适配器
适配LM Studio本地模型API
"""
import requests
import json
import numpy as np
from typing import List, Dict, Any, Optional
from sentence_transformers import SentenceTransformer
import logging

logger = logging.getLogger(__name__)


class LocalEmbeddingModel:
    """本地Embedding模型适配器"""
    
    def __init__(self, base_url: str = "http://localhost:11434", model_name: str = "qwen3-embedding-4b-dwq"):
        self.base_url = base_url
        self.model_name = model_name
        self.embeddings_url = f"{base_url}/v1/embeddings"
        
    def encode(self, texts: List[str], **kwargs) -> np.ndarray:
        """编码文本为向量"""
        if isinstance(texts, str):
            texts = [texts]
        
        try:
            response = requests.post(
                self.embeddings_url,
                json={
                    "model": self.model_name,
                    "input": texts
                },
                timeout=30
            )
            response.raise_for_status()
            
            result = response.json()
            embeddings = []
            
            for item in result["data"]:
                embeddings.append(item["embedding"])
            
            return np.array(embeddings)
            
        except Exception as e:
            logger.error(f"本地embedding模型调用失败: {e}")
            # 降级到本地sentence-transformers模型
            logger.info("降级使用本地sentence-transformers模型")
            fallback_model = SentenceTransformer('all-MiniLM-L6-v2')
            return fallback_model.encode(texts, **kwargs)


class LocalLLMClient:
    """本地LLM客户端"""
    
    def __init__(self, base_url: str = "http://localhost:11434", model_name: str = "qwen3-30b-a3b-mlx@4bit"):
        self.base_url = base_url
        self.model_name = model_name
        self.chat_url = f"{base_url}/v1/chat/completions"
        
    def chat_completion(self, messages: List[Dict[str, str]], **kwargs) -> str:
        """聊天完成"""
        try:
            response = requests.post(
                self.chat_url,
                json={
                    "model": self.model_name,
                    "messages": messages,
                    "temperature": kwargs.get("temperature", 0.7),
                    "max_tokens": kwargs.get("max_tokens", 1000)
                },
                timeout=60
            )
            response.raise_for_status()
            
            result = response.json()
            return result["choices"][0]["message"]["content"]
            
        except Exception as e:
            logger.error(f"本地LLM调用失败: {e}")
            raise


def test_local_models():
    """测试本地模型连接"""
    print("测试本地模型连接...")
    
    # 测试embedding模型
    try:
        embedding_model = LocalEmbeddingModel()
        test_texts = ["这是一个测试文本", "This is a test text"]
        embeddings = embedding_model.encode(test_texts)
        print(f"✅ Embedding模型测试成功，向量维度: {embeddings.shape}")
    except Exception as e:
        print(f"❌ Embedding模型测试失败: {e}")
    
    # 测试LLM模型
    try:
        llm_client = LocalLLMClient()
        messages = [
            {"role": "user", "content": "你好，请简单回复一下"}
        ]
        response = llm_client.chat_completion(messages)
        print(f"✅ LLM模型测试成功，回复: {response[:50]}...")
    except Exception as e:
        print(f"❌ LLM模型测试失败: {e}")


if __name__ == "__main__":
    test_local_models()
