import logging
import os
from logging.handlers import RotatingFileHandler

CODEBASE_LOG_LEVEL = os.getenv("CODEBASE_LOG_LEVEL", "INFO")
LOG_LEVEL_MAP = {
    "debug": logging.DEBUG,
    "info": logging.INFO,
    "warning": logging.WARNING,
    "error": logging.ERROR,
    "critical": logging.CRITICAL,
}
level = LOG_LEVEL_MAP.get(CODEBASE_LOG_LEVEL.lower(), logging.INFO)


# 创建logger
logger = logging.getLogger("codebase")
logger.setLevel(level)

# 创建RotatingFileHandler
log_info_file = "codebase_info.log"
file_handler = RotatingFileHandler(
    filename=log_info_file,
    mode="a",
    maxBytes=50 * 1024 * 1024,  # 10MB 大小限制
    backupCount=5,  # 保留5个备份文件
    encoding="utf-8",
)

log_debug_file = "codebase_debug.log"
debug_file_handler = RotatingFileHandler(
    filename=log_debug_file,
    mode="a",
    maxBytes=50 * 1024 * 1024,  # 10MB 大小限制
    backupCount=5,  # 保留5个备份文件
    encoding="utf-8",
)

# 设置格式
formatter = logging.Formatter(
    fmt="%(asctime)s -%(filename)s:%(lineno)s- [%(levelname)s] %(message)s:",
    datefmt="%Y-%m-%d %H:%M:%S",
)
file_handler.setFormatter(formatter)
file_handler.setLevel(logging.INFO)
debug_file_handler.setFormatter(formatter)
debug_file_handler.setLevel(logging.DEBUG)

# 添加处理器到logger
logger.addHandler(file_handler)
logger.addHandler(debug_file_handler)
