import json
import logging
import os
import re
import sys
import time
import uuid
from concurrent.futures import Thr<PERSON>PoolExecutor
from http import HTTPStatus

import lancedb
import markdown
import redis
import dashscope
from dotenv import load_dotenv
from flask import Flask, jsonify, render_template, request, session
# from lancedb.rerankers import AnswerdotaiRerankers
from openai import OpenAI
from redis import ConnectionPool

load_dotenv()

from code_qa.core.llm.prompts import (  # noqa: E402
    CHAT_SYSTEM_PROMPT,
    HYDE_SYSTEM_PROMPT,
    HYDE_V2_SYSTEM_PROMPT,
    RERANK_PROMPT,
)

# Configuration
CONFIG = {
    "SECRET_KEY": os.urandom(24),
    "REDIS_HOST": "localhost",
    "REDIS_PORT": 6379,
    "REDIS_DB": 0,
    "REDIS_POOL_SIZE": 10,  # Add pool size configuration
    "LOG_FILE": "app.log",
    "LOG_FORMAT": "%(asctime)s - %(message)s",
    "LOG_DATE_FORMAT": "%d-%b-%y %H:%M:%S",
}


# Logging setup
def setup_logging(config):
    # Create a formatter
    formatter = logging.Formatter(
        config["LOG_FORMAT"], datefmt=config["LOG_DATE_FORMAT"]
    )

    # Setup file handler
    file_handler = logging.FileHandler(config["LOG_FILE"])
    file_handler.setFormatter(formatter)

    # Setup console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)

    # Get the logger
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.INFO)

    # Add both handlers
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    return logger


# Database setup
def setup_database(codebase_path):
    normalized_path = os.path.normpath(os.path.abspath(codebase_path))
    codebase_folder_name = os.path.basename(normalized_path)

    # lancedb connection
    uri = os.environ.get("DATABASE_URI", "./.cache/database")
    db = lancedb.connect(uri)

    method_table = db.open_table(codebase_folder_name + "_method")
    class_table = db.open_table(codebase_folder_name + "_class")

    return method_table, class_table


# Application setup
def setup_app():
    app = Flask(__name__)
    app.config.update(CONFIG)

    # Setup logging
    app.logger = setup_logging(app.config)

    # Redis connection pooling setup
    app.redis_pool = ConnectionPool(
        host=app.config["REDIS_HOST"],
        port=app.config["REDIS_PORT"],
        db=app.config["REDIS_DB"],
        max_connections=app.config["REDIS_POOL_SIZE"],
    )

    # Create Redis client using the connection pool
    app.redis_client = redis.Redis(connection_pool=app.redis_pool)

    # Markdown filter
    @app.template_filter("markdown")
    def markdown_filter(text):
        return markdown.markdown(text, extensions=["fenced_code", "tables"])

    return app


# Create the Flask app
app = setup_app()

openai_client = OpenAI(
    api_key=os.getenv("DASHSCOPE_API_KEY"),
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
)
client = OpenAI(
    # 若没有配置环境变量，请用百炼API Key将下行替换为：api_key="sk-xxx",
    api_key=os.getenv("DASHSCOPE_API_KEY"),
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
)


# Initialize the reranker
# reranker = AnswerdotaiRerankers(column="source_code")


# Replace groq_hyde function
def openai_hyde(query):
    chat_completion = openai_client.chat.completions.create(
        # model="gpt-4o-mini",
        # model="qwen2.5-coder-32b-instruct",
        model="qwen-turbo",
        max_tokens=400,
        messages=[
            {"role": "system", "content": HYDE_SYSTEM_PROMPT},
            {
                "role": "user",
                "content": f"Help predict the answer to the query: {query}",
            },
        ],
    )
    app.logger.info(
        f"First HYDE response: {chat_completion.choices[0].message.content}"
    )
    return chat_completion.choices[0].message.content


def openai_hyde_v2(query, temp_context, hyde_query):
    chat_completion = openai_client.chat.completions.create(
        # model="gpt-4o-mini",
        # model="qwen2.5-coder-32b-instruct",
        model="qwen-turbo",
        max_tokens=768,
        messages=[
            {
                "role": "system",
                "content": HYDE_V2_SYSTEM_PROMPT.format(
                    temp_context=temp_context
                ),
            },
            {
                "role": "user",
                "content": f"Predict the answer to the query: {query}",
            },
        ],
    )
    app.logger.info(
        f"Second HYDE response: {chat_completion.choices[0].message.content}"
    )
    return chat_completion.choices[0].message.content


def openai_chat(query, context):
    start_time = time.time()

    chat_completion = client.chat.completions.create(
        # model="Meta-Llama-3.1-70B-Instruct",
        model="qwen-turbo",
        messages=[
            {
                "role": "system",
                "content": CHAT_SYSTEM_PROMPT.format(context=context),
            },
            {
                "role": "user",
                "content": query,
            },
        ],
    )

    chat_time = time.time() - start_time
    app.logger.info(f"Chat response took: {chat_time:.2f} seconds")

    return chat_completion.choices[0].message.content


def rerank_using_small_model(query, context):
    start_time = time.time()

    chat_completion = client.chat.completions.create(
        # model="Meta-Llama-3.1-8B-Instruct",
        model="qwen2.5-coder-7b-instruct",
        messages=[
            {
                "role": "system",
                "content": RERANK_PROMPT.format(context=context),
            },
            {
                "role": "user",
                "content": query,
            },
        ],
    )

    chat_time = time.time() - start_time
    app.logger.info(f"Llama 8B reranker response took: {chat_time:.2f} seconds")

    return chat_completion.choices[0].message.content


def rerank_using_small_model_v2(query, documents: list, threshold=0.1):
    resp = dashscope.TextReRank.call(
        model="gte-rerank-v2",
        query=query,
        documents=documents,
        top_n=10,
        return_documents=True,
    )
    if resp.status_code == HTTPStatus.OK:
        app.logger.info(f"reranker response: {resp}")
        results = resp["output"]["results"]
        results = [
            (item["index"], item["document"]["text"])
            for item in results
            if item["relevance_score"] > threshold
        ]

        return results


def process_input(input_text):
    processed_text = input_text.replace("\n", " ").replace("\t", " ")
    processed_text = re.sub(r"\s+", " ", processed_text)
    processed_text = processed_text.strip()

    return processed_text


def generate_context(query, rerank=False):
    start_time = time.time()

    # First HYDE call
    hyde_query = openai_hyde(query)
    print("============================================")
    print(hyde_query)
    hyde_time = time.time()
    app.logger.info(
        f"First HYDE call took: {hyde_time - start_time:.2f} seconds"
    )

    # Concurrent execution of first database searches
    def search_method_table():
        return method_table.search(hyde_query).limit(5).to_pandas()

    def search_class_table():
        return class_table.search(hyde_query).limit(5).to_pandas()

    with ThreadPoolExecutor(max_workers=2) as executor:
        future_method_docs = executor.submit(search_method_table)
        future_class_docs = executor.submit(search_class_table)
        method_docs = future_method_docs.result()
        class_docs = future_class_docs.result()

    first_search_time = time.time()
    app.logger.info(
        f"First DB search took: {first_search_time - hyde_time:.2f} seconds"
    )

    temp_context = "\n".join(
        method_docs["code"].tolist() + class_docs["source_code"].tolist()
    )
    app.logger.info(f"method_docs:\n {method_docs}")

    # Second HYDE call
    hyde_query_v2 = openai_hyde_v2(query, temp_context, hyde_query)
    second_hyde_time = time.time()
    app.logger.info(
        f"Second HYDE call took: {second_hyde_time - first_search_time:.2f} seconds"
    )

    # Concurrent execution of second database searches
    def search_method_table_v2():
        return method_table.search(hyde_query_v2)

    def search_class_table_v2():
        return class_table.search(hyde_query_v2)

    with ThreadPoolExecutor(max_workers=2) as executor:
        future_method_search = executor.submit(search_method_table_v2)
        future_class_search = executor.submit(search_class_table_v2)
        method_search = future_method_search.result()
        class_search = future_class_search.result()

    search_time = time.time()
    app.logger.info(
        f"Second DB search took: {search_time - second_hyde_time:.2f} seconds"
    )

    # Concurrent reranking if enabled
    app.logger.info(f"Reranking enabled: {rerank}")
    # if rerank:
    #     rerank_start_time = time.time()  # Start timing before reranking

    #     def rerank_method_search():
    #         return method_search.rerank(reranker)

    #     def rerank_class_search():
    #         return class_search.rerank(reranker)

    #     with ThreadPoolExecutor(max_workers=2) as executor:
    #         future_method_search = executor.submit(rerank_method_search)
    #         future_class_search = executor.submit(rerank_class_search)
    #         method_search = future_method_search.result()
    #         class_search = future_class_search.result()

    #     rerank_time = time.time()
    #     app.logger.info(
    #         f"Reranking took: {rerank_time - rerank_start_time:.2f} seconds"
    #     )

    # Set final time reference point
    rerank_time = time.time() if rerank else search_time

    # Fetch top documents
    method_docs = method_search.limit(5).to_list()
    class_docs = class_search.limit(5).to_list()
    final_search_time = time.time()
    app.logger.info(
        f"Final DB search took: {final_search_time - rerank_time:.2f} seconds"
    )
    # def process_methods():
    #     top_3_methods = method_docs[:3]
    #     methods_combined = "\n\n".join(
    #         f"File: {doc['file_path']}\nCode:\n{doc['code']}"
    #         for doc in top_3_methods
    #     )
    #     return rerank_using_small_model(query, methods_combined)

    # def process_classes():
    #     top_3_classes = class_docs[:3]
    #     classes_combined = "\n\n".join(
    #         f"File: {doc['file_path']}\nClass Info:\n{doc['source_code']} References: \n{doc['references']}  \n END OF ROW {i}"
    #         for i, doc in enumerate(top_3_classes)
    #     )
    #     return rerank_using_small_model(query, classes_combined)

    # # Parallel execution of reranking
    # parallel_start_time = time.time()
    # with ThreadPoolExecutor(max_workers=2) as executor:
    #     future_methods = executor.submit(process_methods)
    #     future_classes = executor.submit(process_classes)
    #     methods_context = future_methods.result()
    #     classes_context = future_classes.result()

    # rerank v2
    def process_methods_v2():
        top_5_methods = method_docs[:5]
        methods_combined = [
            f"File: {doc['file_path']}\nCode:\n{doc['code']}"
            for doc in top_5_methods
        ]
        rerank_docs: list = rerank_using_small_model_v2(query, methods_combined)
        origin_methods = [top_5_methods[i] for i, doc in rerank_docs]
        ret_docs = [doc for i, doc in rerank_docs]
        return origin_methods, ret_docs

    def process_classes_V2():
        top_5_classes = class_docs[:5]
        classes_combined = [
            f"File: {doc['file_path']}\nClass Info:\n{doc['source_code']} References: \n{doc['references']}"
            for doc in top_5_classes
        ]
        rerank_docs: list = rerank_using_small_model_v2(query, classes_combined)
        origin_classes = [top_5_classes[i] for i, doc in rerank_docs]
        ret_docs = [doc for i, doc in rerank_docs]
        return origin_classes, ret_docs

    # Parallel execution of reranking
    parallel_start_time = time.time()
    with ThreadPoolExecutor(max_workers=2) as executor:
        future_methods = executor.submit(process_methods_v2)
        future_classes = executor.submit(process_classes_V2)
        methods_docs, methods_context = future_methods.result()
        class_docs, class_context = future_classes.result()

        app.logger.debug(f"methods_docs: {len(methods_docs)}")
        app.logger.debug(f"class_docs: {len(methods_docs)}")

        methods_context = "\n\n".join(methods_context)
        class_context = "\n\n".join(class_context)

    parallel_time = time.time() - parallel_start_time
    final_context = f"{methods_context}\n{class_context}"
    for item in methods_docs:
        del item["method_embeddings"]

    for item in class_docs:
        del item["class_embeddings"]

    app.logger.info(f"Parallel reranking took: {parallel_time:.2f} seconds")
    app.logger.info(f"Final context: {final_context}")
    app.logger.info("Context generation complete.")
    app.logger.info(
        f"Total context generation took: {time.time() - start_time:.2f} seconds"
    )

    return {
        "final_context": final_context,
        "method_docs": methods_docs,
        "class_docs": class_docs,
    }


@app.route("/query", methods=["POST"])
def get_query_context():
    data = request.get_json()
    query = data["query"]
    rerank = data.get("rerank", False)  # Extract rerank value
    user_id = session.get("user_id")
    if user_id is None:
        user_id = str(uuid.uuid4())
        session["user_id"] = user_id

        # Ensure rerank is a boolean
    rerank = True if rerank in [True, "true", "True", "1"] else False
    if "@codebase" in query:
        query = query.replace("@codebase", "").strip()
        context: dict = generate_context(query, rerank)
        app.logger.info("Generated context for query with @codebase.")
        # app.redis_client.set(f"user:{user_id}:chat_context", context)
        print("context".center(100, "="))
        print(context)
        return jsonify(context)
        # context = app.redis_client.get(f"user:{user_id}:chat_context")
        # if context is not None:
        #     context = context.decode("utf-8") 
    else:
        return jsonify({"error": "Invalid query format, use @codebase to start query"})       


@app.route("/", methods=["GET", "POST"])
def home():
    if request.method == "POST":
        if request.headers.get("X-Requested-With") == "XMLHttpRequest":
            # This is an AJAX request
            data = request.get_json()
            query = data["query"]
            rerank = data.get("rerank", False)  # Extract rerank value
            user_id = session.get("user_id")
            if user_id is None:
                user_id = str(uuid.uuid4())
                session["user_id"] = user_id

            # Ensure rerank is a boolean
            rerank = True if rerank in [True, "true", "True", "1"] else False

            if "@codebase" in query:
                query = query.replace("@codebase", "").strip()
                context: dict = generate_context(query, rerank)
                context_str = context.get("final_context", "")
                app.logger.info("Generated context for query with @codebase.")
                app.redis_client.set(
                    f"user:{user_id}:chat_context", context_str
                )
            else:
                context_str = app.redis_client.get(f"user:{user_id}:chat_context")
                if context_str is None:
                    context_str = ""
                else:
                    context_str = context_str.decode()

            # Now, apply reranking during the chat response if needed
            response = openai_chat(
                query, context_str[:8192]
            )  # Adjust as needed

            # Store the conversation history
            redis_key = f"user:{user_id}:responses"
            combined_response = {"query": query, "response": response}
            app.redis_client.rpush(redis_key, json.dumps(combined_response))

            # Return the bot's response as JSON
            return jsonify({"response": response})

    # For GET requests and non-AJAX POST requests, render the template as before
    # Retrieve the conversation history to display
    user_id = session.get("user_id")
    if user_id:
        redis_key = f"user:{user_id}:responses"
        responses = app.redis_client.lrange(redis_key, -5, -1)
        responses = [json.loads(resp.decode()) for resp in responses]
        results = {"responses": responses}
    else:
        results = None

    return render_template("query_form.html", results=results)


def code_app(codebase_path: str):
    # Setup database
    global method_table, class_table
    method_table, class_table = setup_database(codebase_path)

    app.logger.info("Server starting up...")  # Test log message

    # app.debug = True
    app.run(host="0.0.0.0", port=5001)


def main():
    if len(sys.argv) != 2:
        print("Usage: python app.py <codebase_path>")
        sys.exit(1)

    codebase_path = sys.argv[1]
    code_app(codebase_path)


if __name__ == "__main__":
    main()
