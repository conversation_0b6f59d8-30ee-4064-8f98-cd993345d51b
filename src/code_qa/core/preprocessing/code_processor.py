import csv
import os
import sys
from collections import defaultdict
from typing import Dict, List
from dotenv import load_dotenv

from tree_sitter import Node
# from ..parser.treesitter import LanguageEnum, Treesitter
from code_qa.core.parser.treesitter import LanguageEnum, Treesitter

load_dotenv()


# Define your BLACKLIST_DIR, WHITELIST_FILES, NODE_TYPES, and REFERENCE_IDENTIFIERS here
BLACKLIST_DIR = [
    "__pycache__",
    ".pytest_cache",
    ".venv",
    ".git",
    ".idea",
    "venv",
    "env",
    "node_modules",
    "dist",
    "build",
    ".vscode",
    ".github",
    ".gitlab",
    ".angular",
    "cdk.out",
    ".aws-sam",
    ".terraform",
]
WHITELIST_FILES = [".java", ".py", ".js", ".rs", ".ts", ".ets"]
BLACKLIST_FILES = ["docker-compose.yml"]

NODE_TYPES = {
    "python": {"class": "class_definition", "method": "function_definition"},
    "java": {"class": "class_declaration", "method": "method_declaration"},
    "rust": {"class": "struct_item", "method": "function_item"},
    "javascript": {"class": "class_declaration", "method": "method_definition"},
    # Add other languages as needed
    "arkts": {"class": "class_declaration", "method": "method_definition"},
    "typescript": {"class": "class_declaration", "method": "method_definition"},
}

REFERENCE_IDENTIFIERS = {
    "python": {
        "class": "identifier",
        "method": "call",
        "child_field_name": "function",
    },
    "java": {
        "class": "identifier",
        "method": "method_invocation",
        "child_field_name": "name",
    },
    "javascript": {
        "class": "identifier",
        "method": "call_expression",
        "child_field_name": "function",
    },
    "rust": {
        "class": "identifier",
        "method": "call_expression",
        "child_field_name": "function",
    },
    # Add other languages as needed
    # todo arkts
    "arkts": {
        "class": "identifier",
        "method": "call_expression",
        "child_field_name": "function",
    },
    "typescript": {
        "class": "identifier",
        "method": "call_expression",
        "child_field_name": "function",
    },
}


def get_language_from_extension(file_ext: str):
    """
    Map file extensions to the corresponding LanguageEnum.
    :param file_ext: The file extension (e.g., '.py', '.java').
    :return: The corresponding LanguageEnum or None if not found.
    """
    FILE_EXTENSION_LANGUAGE_MAP = {
        ".java": LanguageEnum.JAVA,
        ".py": LanguageEnum.PYTHON,
        ".js": LanguageEnum.JAVASCRIPT,
        ".rs": LanguageEnum.RUST,
        # Add other extensions and languages as needed
        ".ets": LanguageEnum.TYPESCRIPT,
        ".ts": LanguageEnum.TYPESCRIPT,
    }
    return FILE_EXTENSION_LANGUAGE_MAP.get(file_ext)


def load_files(codebase_path: str):
    """
    Load files from the given codebase path and return a list of tuples (file_path, language).
    :param codebase_path: The path to the codebase.
    :return: A list of tuples (file_path, language).
    """
    file_list = []
    for root, dirs, files in os.walk(codebase_path):
        dirs[:] = [d for d in dirs if d not in BLACKLIST_DIR]
        for file in files:
            file_ext = os.path.splitext(file)[1]
            if file_ext in WHITELIST_FILES:
                if file not in BLACKLIST_FILES:
                    file_path = os.path.join(root, file)
                    language = get_language_from_extension(file_ext)
                    if language:
                        file_list.append((file_path, language))
                    else:
                        print(
                            f"Unsupported file extension {file_ext} in file {file_path}. Skipping."
                        )
    return file_list


def parse_code_files(file_list: List[tuple]):
    """
    Parse the code files using Treesitter and return class and method data.
    :param file_list: A list of tuples (file_path, language).
    :return: A tuple of class_data, method_data, all_class_names, all_method_names.
    """
    class_data = []
    method_data = []

    all_class_names = set()
    all_method_names = set()

    files_by_language = defaultdict(list)
    for file_path, language in file_list:
        files_by_language[language].append(file_path)

    for language, files in files_by_language.items():
        treesitter_parser = Treesitter.create_treesitter(language)
        for file_path in files:
            with open(file_path, "r", encoding="utf-8") as file:
                code = file.read()
                file_bytes = code.encode()
                class_nodes, method_nodes = treesitter_parser.parse(file_bytes)

                # Process class nodes
                for class_node in class_nodes:
                    class_name = class_node.name
                    all_class_names.add(class_name)
                    class_data.append(
                        {
                            "file_path": file_path,
                            "class_name": class_name,
                            "constructor_declaration": "",  # Extract if needed
                            "method_declarations": "\n-----\n".join(
                                class_node.method_declarations
                            )
                            if class_node.method_declarations
                            else "",
                            "source_code": class_node.source_code,
                            "references": [],  # Will populate later
                            "start_point": class_node.node.start_point,
                            "end_point": class_node.node.end_point,
                        }
                    )

                # Process method nodes
                for method_node in method_nodes:
                    method_name = method_node.name
                    all_method_names.add(method_name)
                    method_data.append(
                        {
                            "file_path": file_path,
                            "class_name": method_node.class_name
                            if method_node.class_name
                            else "",
                            "name": method_name,
                            "doc_comment": method_node.doc_comment,
                            "source_code": method_node.method_source_code,
                            "references": [],  # Will populate later
                            "start_point": method_node.node.start_point,
                            "end_point": method_node.node.end_point,
                        }
                    )

    return class_data, method_data, all_class_names, all_method_names


def find_references(
    file_list: List[tuple], class_names: list, method_names: list
):
    """
    Find references to class and method names in the codebase.
    :param file_list: A list of tuples (file_path, language).
    :param class_names: A set of class names.
    :param method_names: A set of method names.
    :return: A dictionary of references to class and method names.
    """
    references = {"class": defaultdict(list), "method": defaultdict(list)}
    files_by_language = defaultdict(list)

    # Convert names to sets for O(1) lookup
    class_names = set(class_names)
    method_names = set(method_names)

    for file_path, language in file_list:
        files_by_language[language].append(file_path)

    for language, files in files_by_language.items():
        treesitter_parser = Treesitter.create_treesitter(language)
        for file_path in files:
            with open(file_path, "r", encoding="utf-8") as file:
                code = file.read()
                file_bytes = code.encode()
                tree = treesitter_parser.parser.parse(file_bytes)

                # Single pass through the AST
                stack = [(tree.root_node, None)]
                while stack:
                    node, parent = stack.pop()

                    # Check for identifiers
                    if node.type == "identifier":
                        name = node.text.decode()

                        # Check if it's a class reference
                        if (
                            name in class_names
                            and parent
                            and parent.type
                            in [
                                "type",
                                "class_type",
                                "object_creation_expression",
                            ]
                        ):
                            references["class"][name].append(
                                {
                                    "file": file_path,
                                    "line": node.start_point[0] + 1,
                                    "column": node.start_point[1] + 1,
                                    "text": parent.text.decode(),
                                }
                            )

                        # Check if it's a method reference
                        if (
                            name in method_names
                            and parent
                            and parent.type
                            in ["call_expression", "method_invocation"]
                        ):
                            references["method"][name].append(
                                {
                                    "file": file_path,
                                    "line": node.start_point[0] + 1,
                                    "column": node.start_point[1] + 1,
                                    "text": parent.text.decode(),
                                }
                            )

                    # Add children to stack with their parent
                    stack.extend((child, node) for child in node.children)

    return references


def create_output_directory(codebase_path: str):
    """
    Create the output directory based on the codebase path.
    :param codebase_path: The path to the codebase.
    :return: The path to the output directory.
    """
    normalized_path = os.path.normpath(os.path.abspath(codebase_path))
    codebase_folder_name = os.path.basename(normalized_path)
    cache_csv_dir = os.environ.get("CACHE_CSV_DIR", "./.cache/processed")
    print(f"cache_csv_dir: {cache_csv_dir}")
    output_directory = os.path.join(cache_csv_dir, codebase_folder_name)
    os.makedirs(output_directory, exist_ok=True)
    return output_directory


def write_class_data_to_csv(class_data: list, output_directory: str):
    """
    Write class data to a CSV file.
    :param class_data: A list of class data dictionaries.
    :param output_directory: The path to the output directory.
    """
    output_file = os.path.join(output_directory, "class_data.csv")
    fieldnames = [
        "file_path",
        "class_name",
        "constructor_declaration",
        "method_declarations",
        "source_code",
        "references",
        "start_point",
        "end_point",
    ]
    with open(output_file, "w", newline="", encoding="utf-8") as file:
        writer = csv.DictWriter(file, fieldnames=fieldnames)
        writer.writeheader()
        for row in class_data:
            references = row.get("references", [])
            row["references"] = "; ".join(
                [
                    f"{ref['file']}:{ref['line']}:{ref['column']}"
                    for ref in references
                ]
            )
            writer.writerow(row)
    print(f"Class data written to {output_file}")


def write_method_data_to_csv(method_data: list, output_directory: str):
    """
    Write method data to a CSV file.
    :param method_data: A list of method data dictionaries.
    :param output_directory: The path to the output directory.
    """
    output_file = os.path.join(output_directory, "method_data.csv")
    fieldnames = [
        "file_path",
        "class_name",
        "name",
        "doc_comment",
        "source_code",
        "references",
        "start_point",
        "end_point",
    ]
    with open(output_file, "w", newline="", encoding="utf-8") as file:
        writer = csv.DictWriter(file, fieldnames=fieldnames)
        writer.writeheader()
        for row in method_data:
            references = row.get("references", [])
            row["references"] = "; ".join(
                [
                    f"{ref['file']}:{ref['line']}:{ref['column']}"
                    for ref in references
                ]
            )
            writer.writerow(row)
    print(f"Method data written to {output_file}")


def code_index(codebase_path: str):
    """
    Index the codebase and generate class and method data.
    :param codebase_path: The path to the codebase.
    """
    files = load_files(codebase_path)
    class_data, method_data, class_names, method_names = parse_code_files(files)

    # Find references
    references: dict = find_references(files, class_names, method_names)

    # Map references back to class and method data
    class_data_dict = {cd["class_name"]: cd for cd in class_data}
    method_data_dict = {
        (md["class_name"], md["name"]): md for md in method_data
    }

    for class_name, refs in references["class"].items():
        if class_name in class_data_dict:
            class_data_dict[class_name]["references"] = refs

    for method_name, refs in references["method"].items():
        # Find all methods with this name (since methods might have the same name in different classes)
        for key in method_data_dict:
            if key[1] == method_name:
                method_data_dict[key]["references"] = refs

    # Convert dictionaries back to lists
    class_data = list(class_data_dict.values())
    method_data = list(method_data_dict.values())

    output_directory = create_output_directory(codebase_path)
    write_class_data_to_csv(class_data, output_directory)
    write_method_data_to_csv(method_data, output_directory)


def main():
    # if len(sys.argv) < 2:
    #     print("Please provide the codebase path as an argument.")
    #     sys.exit(1)

    # codebase_path = sys.argv[1]
    codebase_path = "/Users/<USER>/projects/Bitfun_composer_index"

    code_index(codebase_path)


if __name__ == "__main__":
    main()
