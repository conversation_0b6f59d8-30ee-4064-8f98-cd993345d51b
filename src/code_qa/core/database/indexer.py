import os
import sys
from openai import OpenAI
import pandas as pd
import lancedb
from lancedb.embeddings import EmbeddingFunctionRegistry
from lancedb.pydantic import LanceModel, Vector
from lancedb import DBConnection
import tiktoken
from dotenv import load_dotenv
from code_qa.uitls import logger


load_dotenv()

EMBEDDING_DIM = 1024  # OpenAI's dimension
MAX_TOKENS = 4096  # OpenAI's token limit
MODEL_NAME = "text-embedding-v3"

# todo 增加模型选择
if os.environ.get("DASHSCOPE_API_KEY"):
    os.environ["OPENAI_API_KEY"] = os.environ.get("DASHSCOPE_API_KEY")
    registry = EmbeddingFunctionRegistry.get_instance()
    registry.set_var("OPENAI_API_KEY", os.getenv("DASHSCOPE_API_KEY"))

    model = registry.get("openai").create(
        name=MODEL_NAME,
        base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
        max_retries=2,
    )


class Method(LanceModel):
    code: str = model.SourceField()
    method_embeddings: Vector(EMBEDDING_DIM) = model.VectorField()
    file_path: str
    class_name: str
    name: str
    doc_comment: str
    source_code: str
    references: str
    start_point: str
    end_point: str


class Class(LanceModel):
    source_code: str = model.SourceField()
    class_embeddings: Vector(EMBEDDING_DIM) = model.VectorField()
    file_path: str
    class_name: str
    constructor_declaration: str
    method_declarations: str
    references: str
    start_point: str
    end_point: str


def get_name_and_input_dir(codebase_path):
    """
    Extracts the base name of the directory from the given path and creates the output directory.
    Args:
        codebase_path (str): The path to the codebase.
    Returns:
        tuple: A tuple containing the base name of the directory and the output directory path.
    """
    # Normalize and get the absolute path
    normalized_path = os.path.normpath(os.path.abspath(codebase_path))

    # Extract the base name of the directory
    codebase_folder_name = os.path.basename(normalized_path)
    cache_csv_dir = os.environ.get("CACHE_CSV_DIR", "./.cache/processed")

    # Create the output directory under 'processed'
    output_directory = os.path.join(cache_csv_dir, codebase_folder_name)
    os.makedirs(output_directory, exist_ok=True)

    return codebase_folder_name, output_directory


class CodeMethodTable:
    def __init__(self, db: DBConnection, table_name: str):
        self.db = db
        self.table_name = table_name

    def create_table(self, method_data, batch_size=5):
        """
        Creates a table for the given codebase path.
        Args:
            codebase_path (str): The path to the codebase.
        Returns:
            None
        """
        try:
            table = self.db.create_table(
                self.table_name,
                schema=Method,
                mode="overwrite",
                on_bad_vectors="drop",
            )

            method_data["code"] = method_data["source_code"]
            null_rows = method_data.isnull().any(axis=1)

            if null_rows.any():
                logger.debug(
                    "Null values found in method_data. Replacing with 'empty'."
                )
                method_data = method_data.fillna("empty")
            else:
                logger.debug("No null values found in method_data.")

            # Add the concatenated data to the table
            logger.info("Adding method data to table")

            batch_size = 5
            for i in range(len(method_data) // batch_size + 1):
                table.add(method_data[i * batch_size : (i + 1) * batch_size])
                logger.info("Embedded method data successfully")

        except Exception as e:
            logger.error(f"An error occurred: {e}")
            logger.error(f"Dropping table {self.table_name}")
            self.db.drop_table(self.table_name)
            raise e


class CodeClassTable:
    def __init__(self, db: DBConnection, table_name):
        self.db = db
        self.table_name = table_name

    def create_table(self, class_data, batch_size=5):
        """
        Creates a table for the given codebase path.
        Args:
            codebase_path (str): The path to the codebase.
        Returns:
            None
        """
        try:
            class_table = self.db.create_table(
                self.table_name,
                schema=Class,
                mode="overwrite",
                on_bad_vectors="drop",
            )
            null_rows = class_data.isnull().any(axis=1)
            if null_rows.any():
                logger.debug(
                    "Null values found in class_data. Replacing with 'empty'."
                )
                class_data = class_data.fillna("")
            else:
                logger.debug("No null values found in class_data.")

            # row wise
            class_data["source_code"] = class_data.apply(
                lambda row: "File: "
                + row["file_path"]
                + "\n\n"
                + "Class: "
                + row["class_name"]
                + "\n\n"
                + "Source Code:\n"
                + self.clip_text_to_max_tokens(row["source_code"], MAX_TOKENS)
                + "\n\n",
                axis=1,
            )

            if len(class_data) == 0:
                columns = [
                    "source_code",
                    "file_path",
                    "class_name",
                    "constructor_declaration",
                    "method_declarations",
                    "references",
                ]
                empty_data = {col: ["empty"] for col in columns}

                class_data = pd.DataFrame(empty_data)

            logger.info("Adding class data to table")
            for i in range(len(class_data) // batch_size + 1):
                class_table.add(
                    class_data[i * batch_size : (i + 1) * batch_size]
                )
            logger.info("Embedded class data successfully")

        except Exception as e:
            logger.error(f"An error occurred: {e}")
            logger.error(f"Dropping table {self.table_name}")
            self.db.drop_table(self.table_name)
            raise e


class CodeIndexer:
    def __init__(
        self,
        codebase_path,
        model_name="text-embedding-v3",
        embedding_dim=1024,
        max_tokens=4096,
    ):
        self.codebase_path = codebase_path
        global MODEL_NAME
        global EMBEDDING_DIM
        global MAX_TOKENS
        MODEL_NAME = model_name
        EMBEDDING_DIM = embedding_dim
        MAX_TOKENS = max_tokens
        global model
        self.model_name = model_name
        self.embedding_dim = embedding_dim
        self.max_tokens = max_tokens

    @staticmethod
    def get_name_and_input_dir(codebase_path):
        """
        Extracts the base name of the directory from the given path and creates the output directory.
        Args:
            codebase_path (str): The path to the codebase.
        Returns:
            tuple: A tuple containing the base name of the directory and the output directory path.
        """
        # Normalize and get the absolute path
        normalized_path = os.path.normpath(os.path.abspath(codebase_path))

        # Extract the base name of the directory
        codebase_folder_name = os.path.basename(normalized_path)
        cache_csv_dir = os.environ.get("CACHE_CSV_DIR", "./.cache/processed")

        # Create the output directory under 'processed'
        output_directory = os.path.join(cache_csv_dir, codebase_folder_name)
        os.makedirs(output_directory, exist_ok=True)

        return codebase_folder_name, output_directory

    def build(self):
        """
        Embeds the codebase using the specified embedding model and stores the embeddings in a LanceDB table.
        Args:
            codebase_path (str): The path to the codebase.
        Returns:
            None
        """
        codebase_path = self.codebase_path
        table_name, input_directory = self.get_name_and_input_dir(codebase_path)
        method_data_file = os.path.join(input_directory, "method_data.csv")
        class_data_file = os.path.join(input_directory, "class_data.csv")
        method_data = pd.read_csv(method_data_file)
        class_data = pd.read_csv(class_data_file)
        logger.debug(method_data.head(5))
        logger.debug(class_data.head(5))

        uri = os.environ.get("DATABASE_URI", "./.cache/database")
        db = lancedb.connect(uri)

        method_table = CodeMethodTable(db, table_name + "_method")
        method_table.create_table(method_data)
        class_table = CodeClassTable(db, table_name + "_class")
        class_table.create_table(class_data)


class MarkdownTable:
    def __init__(self, db: DBConnection):
        self.db = db

    def create_table(self, table_name, markdown_data, batch_size=5):
        # todo add markdown table
        pass


# todo build SpecialIndexer for md,sh or other special files
class SpecialIndexer(CodeIndexer):
    def build(self):
        try:
            batch_size = 5
            special_files = self.get_special_files()
            special_contents = self.process_special_files(special_files)
            markdown_table = MarkdownTable(self.db)
            if len(special_contents) > 0:
                markdown_df = self.create_markdown_dataframe(special_contents)
                print(f"Adding {len(markdown_df)} special files to table")
                for i in range(len(markdown_df) // batch_size + 1):
                    markdown_table.add(
                        markdown_df[i * batch_size : (i + 1) * batch_size]
                    )
        except Exception as e:
            logger.error(f"An error occurred: {e}")
            if self.codebase_path in self.db:
                logger.error(f"Dropping table {self.codebase_path}")
                self.db.drop_table(self.codebase_path)
            raise e

    @staticmethod
    def get_special_files(self):
        """
        Returns a list of all .md and .sh files in the given directory and its subdirectories.
        """
        md_files = []
        for root, _, files in os.walk(self.codebase_path):
            for file in files:
                if file.endswith((".md", ".sh")):
                    full_path = os.path.join(root, file)
                    md_files.append(full_path)
        return md_files

    @staticmethod
    def process_special_files(md_files):
        """
        Reads and processes the content of all.md and.sh files.
        """
        contents = {}
        for file_path in md_files:
            with open(file_path, "r", encoding="utf-8") as file:
                contents[file_path] = file.read()
        return contents

    @staticmethod
    def create_markdown_dataframe(self, markdown_contents):
        """
        Creates a DataFrame from the markdown_contents dictionary.
        """

        df = pd.DataFrame(
            list(markdown_contents.items()),
            columns=["file_path", "source_code"],
        )

        # Format the source_code with file information and apply clipping
        df["source_code"] = df.apply(
            lambda row: f"File: {row['file_path']}\n\nContent:\n{self.clip_text_to_max_tokens(row['source_code'], MAX_TOKENS)}\n\n",
            axis=1,
        )

        # Add placeholder "empty" for the other necessary columns
        for col in [
            "class_name",
            "constructor_declaration",
            "method_declarations",
            "references",
        ]:
            df[col] = "empty"
        return df

    @staticmethod
    def clip_text_to_max_tokens(
        self, text, max_tokens, encoding_name="cl100k_base"
    ):
        encoding = tiktoken.get_encoding(encoding_name)
        tokens = encoding.encode(text)
        original_token_count = len(tokens)

        print(f"\nOriginal text ({original_token_count} tokens):")
        print("=" * 50)
        print(
            text[:200] + "..." if len(text) > 200 else text
        )  # Print first 200 chars for preview

        if original_token_count > max_tokens:
            tokens = tokens[:max_tokens]
            clipped_text = encoding.decode(tokens)
            print(f"\nClipped text ({len(tokens)} tokens):")
            print("=" * 50)
            print(
                clipped_text[:200] + "..."
                if len(clipped_text) > 200
                else clipped_text
            )
            return clipped_text

        return text


def main():
    if len(sys.argv) != 2:
        print("Usage: python script.py <code_base_path>")
        sys.exit(1)

    codebase_path = sys.argv[1]

    code_indexer = CodeIndexer(codebase_path)
    code_indexer.build()


if __name__ == "__main__":
    # main()
    logger.info("Hello World")
