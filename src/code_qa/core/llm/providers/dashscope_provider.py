"""
For models other than those from OpenAI, use LiteLLM if possible.
Create all models managed by Ollama here, since they need to talk to ollama server.
"""

import sys
import os
from copy import deepcopy
from typing import Literal

import timeout_decorator
from dashscope import ChatResponse, Generation

from code_qa.core.llm.common import Model, MODEL_TEMP


class DashScopeModel(Model):
    """
    Base class for creating Singleton instances of Ollama models.
    """

    _instances = {}

    def __new__(cls):
        if cls not in cls._instances:
            cls._instances[cls] = super().__new__(cls)
            cls._instances[cls]._initialized = False
        return cls._instances[cls]

    def __init__(self, name: str):
        if self._initialized:
            return
        # local models are free
        super().__init__(name, 0.0, 0.0)
        self._initialized = True

    def setup(self) -> None:
        """
        Check API key.
        """
        self.check_api_key()

    @timeout_decorator.timeout(120)  # 2 min
    def send_empty_request(self):
        """
        Send an empty request to the model, for two purposes
        (1) check whether the model is up and running
        (2) preload the model for faster response time (models will be kept in memory for 5 mins after loaded)
        (see https://github.com/ollama/ollama/blob/main/docs/faq.md#how-can-i-pre-load-a-model-to-get-faster-response-times)
        """
        Generation.chat(model=self.name, messages=[])

    def check_api_key(self) -> str:
        key_name = "DASHSCOPE_API_KEY"
        key = os.getenv(key_name)
        if not key:
            print(f"Please set the {key_name} env var")
            sys.exit(1)
        return key

    def extract_resp_content(
        self, chat_completion_message: ChatResponse
    ) -> str:
        """
        Given a chat completion message, extract the content from it.
        """
        content = chat_completion_message.content
        if content is None:
            return ""
        else:
            return content

    def call(
        self,
        messages: list[dict],
        top_p=1,
        tools=None,
        response_format: Literal["text", "json_object"] = "text",
        temperature: float | None = None,
        **kwargs,
    ):
        stop_words = ["assistant", "\n\n \n\n"]
        json_stop_words = deepcopy(stop_words)
        json_stop_words.append("```")
        json_stop_words.append(" " * 10)
        # FIXME: ignore tools field since we don't use tools now

        if temperature is None:
            temperature = MODEL_TEMP

        try:
            # build up options for ollama
            options = {
                "temperature": temperature,
                "top_p": top_p,
            }
            if response_format == "json_object":
                # additional instructions for json mode
                json_instruction = {
                    "role": "user",
                    "content": "Stop your response after a valid json is generated.",
                }
                messages.append(json_instruction)
                # give more stop words and lower max_token for json mode
                options.update({"stop": json_stop_words, "num_predict": 128})
                response = Generation.chat(
                    model=self.name,
                    messages=messages,
                    format="json",
                    options=options,
                    stream=False,
                )
            else:
                options.update({"stop": stop_words, "num_predict": 1024})
                response = Generation.chat(
                    model=self.name,
                    messages=messages,
                    options=options,
                    stream=False,
                )

            resp_msg = response.get("message", None)
            if resp_msg is None:
                return ""

            content: str = resp_msg.get("content", "")
            return content

        except Exception as e:
            # FIXME: catch appropriate exception from ollama
            raise e


class Qwen2D5Coder32B(DashScopeModel):
    def __init__(self):
        super().__init__("qwen2.5-coder:32b")
        self.note = "Qwen2.5 Coder 32B model."
