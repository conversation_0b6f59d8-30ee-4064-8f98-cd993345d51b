"""
For models other than those from OpenAI, use LiteLLM if possible.
Create all models managed by Ollama here, since they need to talk to ollama server.
"""

import sys
from copy import deepcopy
from typing import Literal, cast

import ollama
from openai import OpenAI
from openai.types.chat.chat_completion import ChatCompletionMessage
import timeout_decorator

from code_qa.core.llm.common import Model, ModelOptions


class OllamaModelOptions(ModelOptions):
    top_p: float = 0.9
    top_k: int = 40


class OllamaModel(Model):
    """
    Base class for creating Singleton instances of Ollama models.
    """

    _instances = {}

    def __new__(cls):
        if cls not in cls._instances:
            cls._instances[cls] = super().__new__(cls)
            cls._instances[cls]._initialized = False
        return cls._instances[cls]

    def __init__(self, name: str):
        if self._initialized:
            return
        # local models are free
        super().__init__(name, 0.0, 0.0)
        self._initialized = True

    def setup(self) -> None:
        """
        Check API key.
        """
        self.check_api_key()
        try:
            self.send_empty_request()
            print(f"Model {self.name} is up and running.")
        except timeout_decorator.TimeoutError as e:
            print(
                "Ollama server is taking too long (more than 2 mins) to respond. Please check whether it's running.",
                e,
            )
            sys.exit(1)

    @timeout_decorator.timeout(120)  # 2 min
    def send_empty_request(self):
        """
        Send an empty request to the model, for two purposes
        (1) check whether the model is up and running
        (2) preload the model for faster response time (models will be kept in memory for 5 mins after loaded)
        (see https://github.com/ollama/ollama/blob/main/docs/faq.md#how-can-i-pre-load-a-model-to-get-faster-response-times)
        """
        ollama.chat(model=self.name, messages=[])

    def check_api_key(self) -> str:
        return "No key required for local models."

    def extract_resp_content(
        self, chat_completion_message: ChatCompletionMessage
    ) -> str:
        """
        Given a chat completion message, extract the content from it.
        """
        content = chat_completion_message.content
        if content is None:
            return ""
        else:
            return content

    def call(
        self,
        messages: list[dict],
        **kwargs,
    ):
        # 初始化客户端，指向 Ollama 的本地服务
        client = OpenAI(
            base_url="http://localhost:11434/v1",  # Ollama API 地址
            api_key="ollama",  # Ollama 默认无需真实 API Key，填任意值即可
        )

        # 发送请求
        response = client.chat.completions.create(
            model=self.name,  # 指定模型
            messages=messages,
            max_tokens=512,  # 最大生成 token 数
            **ModelOptions().model_dump(),
        )
        return response


class Qwen2D5Coder3B(OllamaModel):
    def __init__(self):
        super().__init__("qwen2.5-coder:3b")
        self.note = "Qwen2.5 Coder 3B model."
