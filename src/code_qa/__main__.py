import argparse
from typing import List, <PERSON><PERSON>
from code_qa.core.preprocessing.code_processor import code_index
from code_qa.core.database.indexer import CodeIndexer
from code_qa.web.app import code_app


def _code_index(args: List[str]) -> None:
    code_index(args.path)


def _code_embedding(args: List[str]) -> None:
    CodeIndexer(args.path)


def _code_comment(args: List[str]) -> None:
    # code_comment(args.path)
    return


def _code_app(args: List[str]) -> None:
    code_app(args.path)
    return


def parse_args() -> Tuple[str, str]:
    """
    This function parses the command line arguments.
    """
    parser = argparse.ArgumentParser(description="Code QA")
    sub_parser = parser.add_subparsers(help="sub-command help")

    # index
    index_parser = sub_parser.add_parser("index", help="index repository")
    index_parser.add_argument("-p", "--path", type=str, help="repository path")
    index_parser.set_defaults(func=_code_index)

    # embedding
    emb_parser = sub_parser.add_parser("embed", help="embedding repository")
    emb_parser.add_argument("-p", "--path", type=str, help="repository path")
    emb_parser.set_defaults(func=_code_embedding)

    # comment
    com_parser = sub_parser.add_parser("comment", help="add comment code")
    com_parser.add_argument("-p", "--path", type=str, help="repository path")
    com_parser.set_defaults(func=_code_comment)

    # run app
    app_parser = sub_parser.add_parser("app", help="run app")
    app_parser.add_argument("-p", "--path", type=str, help="repository path")
    app_parser.set_defaults(func=_code_app)

    args = parser.parse_args()
    return args


def main():
    """
    This is the main function for the code_qa package.
    """
    args = parse_args()
    args.func(args)


if __name__ == "__main__":
    main()
