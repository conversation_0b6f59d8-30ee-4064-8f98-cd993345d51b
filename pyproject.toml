[project]
name = "code_qa"
version = "0.2.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "cohere>=5.15.0",
    "dashscope>=1.23.3",
    "flask>=3.1.1",
    "lancedb>=0.22.0",
    "markdown>=3.8",
    "openai>=1.79.0",
    "pandas>=2.2.3",
    "pydantic-ai>=0.2.4",
    "python-dotenv",
    "redis>=6.1.0",
    "rerankers[transformers]>=0.9.1.post1",
    "tiktoken>=0.9.0",
    "tree-sitter==0.21.3",
    "tree-sitter-languages>=1.10.2",
]

[build-system]
requires = ["setuptools>=42", "wheel"]
build-backend = "setuptools.build_meta"

[project.scripts]
code-qa = "code_qa.__main__:main"

[tool.setuptools.dynamic]
version = {attr = "code_qa.__version__"}
